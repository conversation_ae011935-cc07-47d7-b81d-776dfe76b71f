# DYTT电影天堂爬虫

这是一个用于爬取电影天堂网站电影信息的Scrapy爬虫项目，采用两阶段爬取架构。

## 功能特点

- **两阶段爬取架构**：先收集链接，再爬取详情
- 爬取电影基本信息（标题、译名、片名、年份、导演、演员、类型、评分、简介等）
- 提取电影下载链接（磁力链接、迅雷链接等）
- 支持数据存储到JSON文件和MySQL数据库
- 任务调度和重试机制
- 可配置的爬取延迟和并发控制
- **完整的日志系统**：多级别日志记录、文件轮转、错误追踪
- 详细的统计信息和运行监控
- **防封IP策略**：多层防护机制，有效避免IP被封禁
- **智能编码处理**：自动检测网页编码，解决中文乱码问题

## 安装依赖

```bash
pip install -r requirements.txt
```

主要依赖包括：
- `scrapy`：爬虫框架
- `pymysql`：MySQL数据库连接
- `chardet`：编码检测库（用于解决中文乱码问题）
- `schedule`：任务调度
- `requests`：HTTP请求库

## 使用方法

### 两阶段爬取（推荐）

#### 第一阶段：收集电影链接

```bash
# 收集所有分类的电影链接
python run_link_collector.py
```

这会爬取以下分类的电影链接：
- 最新电影
- 华语电影
- 欧美电影
- 日韩电影

#### 第二阶段：爬取电影详情

```bash
# 单次运行，爬取10个电影详情
python run_detail_crawler.py --mode once --batch-size 10

# 调度器模式，每5分钟自动爬取10个电影详情
python run_detail_crawler.py --mode scheduler --batch-size 10 --interval 300
```

### 链接管理

```bash
# 查看统计信息
python manage_movie_links.py stats

# 列出所有链接
python manage_movie_links.py list

# 列出待爬取的链接
python manage_movie_links.py list --status pending

# 重置失败的链接
python manage_movie_links.py reset-failed

# 重置爬取中的链接
python manage_movie_links.py reset-crawling

# 删除已完成的链接
python manage_movie_links.py delete-completed

# 导出链接
python manage_movie_links.py export --filename my_links.txt

# 指定MySQL连接参数（可选）
python manage_movie_links.py stats --host 127.0.0.1 --port 3306 --user root --password root --database dytt2026
```

### 数据库管理

```bash
# 初始化数据库（首次使用）
python init_database_detailed.py

# 检查数据库内容
python check_detailed_db.py

# 清空数据库
python clear_database.py

# 检查最新电影
python check_latest_movies.py
```

## 数据库结构

### MySQL数据库配置

默认配置（可在settings.py中修改）：
- 主机: 127.0.0.1
- 端口: 3306
- 用户名: root
- 密码: root
- 数据库名: dytt2026

### 电影链接表 (movie_links)

- `id`: 主键
- `title`: 电影标题
- `url`: 电影详情页URL
- `category`: 电影分类
- `list_page_url`: 来源列表页URL
- `crawl_time`: 爬取时间
- `status`: 状态 (pending, crawling, completed, failed)
- `retry_count`: 重试次数
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 配置说明

### 链接收集器配置

- `DOWNLOAD_DELAY`: 2秒
- `CONCURRENT_REQUESTS_PER_DOMAIN`: 2
- 支持翻页爬取

### 详情爬取器配置

- `DOWNLOAD_DELAY`: 3秒
- `CONCURRENT_REQUESTS_PER_DOMAIN`: 1
- 批量处理，默认每批10个
- 失败重试，最多3次

## 工作流程

1. **链接收集阶段**：
   - 爬取各分类列表页
   - 提取电影详情页链接
   - 存储到MySQL数据库
   - 支持翻页和去重

2. **详情爬取阶段**：
   - 从MySQL数据库读取待爬取链接
   - 批量爬取电影详情
   - 更新链接状态
   - 支持重试和错误处理

3. **任务调度**：
   - 支持单次运行和调度器模式
   - 可配置批次大小和间隔时间
   - 自动处理失败重试

## 优势

1. **稳定性**：分离链接收集和详情爬取，降低失败风险
2. **可恢复性**：支持断点续爬，失败重试
3. **可控性**：精确控制爬取速度和并发
4. **可监控性**：详细的状态跟踪和统计信息
5. **可扩展性**：易于添加新的分类和数据字段

## 注意事项

1. 请遵守网站的robots.txt协议
2. 合理设置爬取延迟，避免对目标网站造成过大压力
3. 定期清理已完成的链接，避免数据库过大
4. 仅用于学习和研究目的，请勿用于商业用途

## 日志系统

项目集成了完整的日志记录系统，提供详细的运行信息和错误追踪。

### 日志功能

- **多级别日志**：DEBUG、INFO、WARNING、ERROR、CRITICAL
- **多输出目标**：控制台输出 + 文件记录
- **文件轮转**：自动按大小轮转日志文件，保留历史记录
- **错误追踪**：单独的错误日志文件，包含完整堆栈信息
- **时间戳记录**：精确的时间戳和执行时长统计

### 日志文件

所有日志文件存储在 `logs/` 目录下，每个爬虫都有独立的日志文件：

#### 通用日志文件
- `spider.log`：通用日志文件，记录所有INFO及以上级别的日志
- `error.log`：通用错误日志文件，仅记录ERROR和CRITICAL级别的日志

#### 爬虫专用日志文件
- `dytt_spider.log`：dytt爬虫的专用日志文件
- `dytt_spider_error.log`：dytt爬虫的错误日志文件
- `link_collector.log`：链接收集爬虫的专用日志文件
- `link_collector_error.log`：链接收集爬虫的错误日志文件
- `detail_crawler.log`：详情爬虫的专用日志文件
- `detail_crawler_error.log`：详情爬虫的错误日志文件
- 历史文件：`*.log.1`、`*.log.2` 等（自动轮转）

### 日志内容

日志记录包含以下信息：
- 爬虫启动/结束时间和总耗时
- 配置参数（下载延迟、并发数等）
- 数据库连接和查询统计
- 爬取进度和成功/失败统计
- 详细的错误信息和堆栈跟踪

### 查看日志

```bash
# 查看通用日志
tail -f logs/spider.log

# 查看特定爬虫的日志
tail -f logs/dytt_spider.log          # dytt爬虫日志
tail -f logs/link_collector.log       # 链接收集爬虫日志
tail -f logs/detail_crawler.log       # 详情爬虫日志

# 查看错误日志
tail -f logs/dytt_spider_error.log    # dytt爬虫错误日志
tail -f logs/link_collector_error.log # 链接收集爬虫错误日志
tail -f logs/detail_crawler_error.log  # 详情爬虫错误日志
```

## 失败页面管理功能

### 功能概述

本系统新增了失败页面管理功能，能够自动记录爬虫过程中失败的列表页URL，并在下次运行时自动重试这些失败的页面，确保数据的完整性。

### 数据库表结构

#### failed_list_pages 表

该表用于存储失败的列表页信息：

- `id`: 主键，自增ID
- `url`: 失败页面的URL（唯一索引）
- `category`: 页面分类（如"最新电影"）
- `failure_reason`: 失败原因
- `status_code`: HTTP状态码
- `retry_count`: 重试次数
- `last_attempt_time`: 最后尝试时间
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `is_resolved`: 是否已解决（布尔值）
- `notes`: 备注信息

### 自动重试机制

#### 失败页面记录

爬虫在运行过程中会自动记录以下类型的失败页面：

1. **no_movie_links**: 页面中未找到电影链接
2. **parse_error**: 页面解析错误
3. **request_failed**: 请求失败（网络错误、超时等）

#### 重试逻辑

- 爬虫启动时会自动从数据库加载所有未解决的失败页面
- 成功处理的页面会被标记为已解决
- 失败的页面会更新重试次数和最后尝试时间
- 支持跨运行的失败页面重试

### 失败页面管理工具

#### 查看统计信息

```bash
python manage_failed_pages.py stats
```

显示内容：
- 总体统计（总计、已解决、未解决页面数量）
- 分类统计
- 失败原因统计
- 重试次数分布

#### 查看失败页面列表

```bash
# 查看所有失败页面（默认显示20条）
python manage_failed_pages.py list

# 查看前50条失败页面
python manage_failed_pages.py list --limit 50

# 只查看未解决的失败页面
python manage_failed_pages.py list --unresolved-only

# 查看特定分类的失败页面
python manage_failed_pages.py list --category "最新电影"
```

#### 重置页面状态

```bash
# 重置所有页面为未解决状态
python manage_failed_pages.py reset-all

# 重置特定分类的页面
python manage_failed_pages.py reset-category "最新电影"

# 重置特定页面ID
python manage_failed_pages.py reset-page 1
```

#### 标记页面为已解决

```bash
# 标记特定页面为已解决
python manage_failed_pages.py resolve 1
```

#### 删除已解决的页面

```bash
python manage_failed_pages.py clean-resolved
```

#### 导出失败页面

```bash
# 导出所有失败页面到CSV文件
python manage_failed_pages.py export --filename failed_pages.csv

# 只导出未解决的页面
python manage_failed_pages.py export --filename failed_pages.csv --unresolved-only
```

### 使用场景

#### 1. 增量爬取

当爬虫因为各种原因（网络问题、反爬虫机制等）中断时，下次运行会自动重试失败的页面，确保数据完整性。

#### 2. 问题诊断

通过查看失败原因统计，可以快速识别爬虫遇到的主要问题：
- 网络连接问题
- 页面结构变化
- 反爬虫机制触发

#### 3. 性能优化

通过分析重试次数分布，可以优化爬虫的重试策略和错误处理机制。

### 最佳实践

1. **定期查看统计**: 运行爬虫后查看失败页面统计，了解爬虫运行状况
2. **及时清理**: 定期删除已解决的失败页面，保持数据库整洁
3. **分析失败原因**: 根据失败原因统计优化爬虫策略
4. **备份重要数据**: 在大规模清理前备份失败页面数据

通过这套失败页面管理系统，可以显著提高爬虫的数据完整性和可靠性，确保不会因为临时性问题而丢失重要的爬取目标。

## 防封IP完整指南

### 快速开始

#### 立即可用的配置

如果你想立即开始使用防封IP功能，只需要在现有的爬虫脚本中添加以下配置：

```python
# 在run_link_collector.py或run_detail_crawler.py中添加
custom_settings = {
    # 导入防封IP配置
    **dict(vars(__import__('dyttspider.anti_ban_settings', fromlist=['']))),
    
    # 或者手动设置关键参数
    'CONCURRENT_REQUESTS': 2,
    'DOWNLOAD_DELAY': 5,
    'RANDOMIZE_DOWNLOAD_DELAY': 1.0,
    'AUTOTHROTTLE_ENABLED': True,
    'AUTOTHROTTLE_START_DELAY': 5,
    'AUTOTHROTTLE_MAX_DELAY': 30,
}
```

#### 验证配置是否生效

运行爬虫后，检查日志中是否出现以下信息：

```
[INFO] 使用User-Agent: Mozilla/5.0...
[INFO] 延迟 3.25 秒
[INFO] AutoThrottle: 调整延迟到 4.2 秒
```

### 策略概述

#### 核心防封策略

1. **请求频率控制**
   - 智能延迟机制
   - 并发数限制
   - 自动限速

2. **身份伪装**
   - 随机User-Agent轮换
   - 随机HTTP请求头
   - 会话管理

3. **IP轮换**
   - 代理池管理
   - 失败代理自动切换
   - 代理健康检测

4. **智能重试**
   - 状态码识别
   - 指数退避算法
   - 异常处理

5. **行为模拟**
   - Cookie管理
   - 会话保持
   - 真实浏览器行为模拟

### 配置说明

#### 基础配置

在 `anti_ban_settings.py` 中调整以下参数：

```python
# 并发控制
CONCURRENT_REQUESTS = 4  # 全局并发数
CONCURRENT_REQUESTS_PER_DOMAIN = 1  # 单域名并发数

# 延迟设置
DOWNLOAD_DELAY = 3  # 基础延迟（秒）
RANDOMIZE_DOWNLOAD_DELAY = 0.5  # 随机因子
```

#### 代理配置

```python
# 在anti_ban_settings.py中配置代理列表
PROXY_LIST = [
    'http://proxy1.example.com:8080',
    'http://username:<EMAIL>:8080',
    'socks5://proxy3.example.com:1080',
]
```

#### 自动限速配置

```python
# 启用自动限速（推荐）
AUTOTHROTTLE_ENABLED = True
AUTOTHROTTLE_START_DELAY = 3
AUTOTHROTTLE_MAX_DELAY = 30
AUTOTHROTTLE_TARGET_CONCURRENCY = 1.0
```

### 中间件详解

#### 1. RandomUserAgentMiddleware

**功能**：随机轮换User-Agent

**特点**：
- 包含14种常见浏览器UA
- 覆盖Windows、macOS、Linux、移动端
- 自动为每个请求随机选择

#### 2. RandomHeadersMiddleware

**功能**：添加随机HTTP请求头

**特点**：
- 随机Accept、Accept-Language、Accept-Encoding
- 模拟真实浏览器行为
- 可选请求头随机添加

#### 3. ProxyMiddleware

**功能**：代理IP管理

**特点**：
- 支持HTTP/HTTPS/SOCKS5代理
- 失败代理自动标记
- 代理池自动轮换

#### 4. DelayMiddleware

**功能**：智能延迟控制

**特点**：
- 根据响应状态动态调整延迟
- 429状态码自动增加延迟
- 成功响应逐渐减少延迟

#### 5. EnhancedRetryMiddleware

**功能**：增强重试机制

**特点**：
- 针对反爬虫状态码优化
- 智能重试延迟
- 网络异常处理

#### 6. SessionMiddleware

**功能**：会话管理

**特点**：
- 自动Cookie管理
- 会话状态保持
- 域名级别Cookie隔离

### 实战经验

#### 经验1：渐进式调优

**问题**：一开始就使用激进配置容易被封。

**解决方案**：
1. 第一周：保守配置（延迟5秒，并发1）
2. 第二周：观察日志，适当提速（延迟3秒，并发2）
3. 第三周：找到最佳平衡点（延迟2秒，并发4）

#### 经验2：监控关键指标

**关键指标**：
- 成功率：应保持在90%以上
- 403/429状态码比例：应低于5%
- 平均响应时间：不应异常增长

**监控脚本**：
```bash
# 统计成功率
grep "HTTP 200" logs/spider.log | wc -l
grep "HTTP" logs/spider.log | wc -l

# 统计封禁情况
grep "HTTP 403\|HTTP 429" logs/spider.log | wc -l
```

#### 经验3：时间策略

**发现**：不同时间段的封禁严格程度不同。

**策略**：
- 凌晨2-6点：相对宽松，可以适当提速
- 上午9-12点：较为严格，建议保守配置
- 下午2-6点：最严格，建议暂停或极保守配置
- 晚上8-11点：中等严格，正常配置

### 常见问题解决

#### 问题1：突然大量403错误

**原因分析**：
- IP被临时封禁
- User-Agent被识别
- 请求频率过高

**解决方案**：
```python
# 立即调整配置
DOWNLOAD_DELAY = 10  # 大幅增加延迟
CONCURRENT_REQUESTS = 1  # 降低并发

# 更换User-Agent池
# 考虑使用代理
```

#### 问题2：响应时间异常增长

**原因分析**：
- 网站负载增加
- 被限速但未完全封禁
- 代理质量下降

**解决方案**：
```python
# 启用超时检测
DOWNLOAD_TIMEOUT = 30

# 增加重试次数
RETRY_TIMES = 3

# 更换代理或增加延迟
```

### 配置模板

#### 保守模式（高成功率）
```python
CONCURRENT_REQUESTS = 2
DOWNLOAD_DELAY = 5
RANDOMIZE_DOWNLOAD_DELAY = 1.0
AUTOTHROTTLE_ENABLED = True
AUTOTHROTTLE_START_DELAY = 5
```

#### 平衡模式（推荐）
```python
CONCURRENT_REQUESTS = 4
DOWNLOAD_DELAY = 3
RANDOMIZE_DOWNLOAD_DELAY = 0.5
AUTOTHROTTLE_ENABLED = True
AUTOTHROTTLE_START_DELAY = 3
```

#### 激进模式（高速度）
```python
CONCURRENT_REQUESTS = 8
DOWNLOAD_DELAY = 1
RANDOMIZE_DOWNLOAD_DELAY = 0.3
AUTOTHROTTLE_ENABLED = True
AUTOTHROTTLE_START_DELAY = 1
```

### 防封IP总结

防封IP是一个需要持续优化的过程，关键在于：

1. **保守起步**：宁可慢一点也不要被封
2. **持续监控**：密切关注各项指标
3. **灵活调整**：根据实际情况调整策略
4. **多层防护**：同时使用多种防封技术
5. **合规操作**：遵守网站规则和法律法规

记住：**稳定的低速爬取比快速的频繁封禁要好得多**。

## 编码问题修复

本项目已集成智能编码处理功能，自动解决中文网站的乱码问题。

### 编码处理特性

- **自动编码检测**：使用 `chardet` 库自动检测网页编码
- **多编码支持**：支持GB2312、GBK、GB18030、Big5等中文编码
- **乱码检测**：智能识别解码结果中的乱码字符
- **自动重试**：检测到乱码时自动使用正确编码重新解析
- **全爬虫覆盖**：所有三个爬虫都已集成编码处理功能

### 工作原理

1. **编码检测**：使用 `chardet.detect()` 检测网页的实际编码
2. **置信度判断**：只有当检测置信度 > 0.7 时才使用检测到的编码
3. **多重尝试**：如果检测失败，依次尝试 GBK、GB2312、GB18030、Big5 编码
4. **乱码检测**：检查解码后的文本中非中文字符的比例，如果超过30%则认为可能存在乱码
5. **重新解析**：如果检测到乱码，使用正确编码重新创建Selector进行解析

### 修改的文件

- **dytt_spider.py**：在 `parse()` 和 `parse_movie()` 方法中添加编码检测逻辑
- **detail_crawler_spider.py**：在 `parse_movie()` 方法中添加编码处理机制
- **link_collector_spider.py**：在 `parse()` 方法中添加链接提取的编码处理
- **requirements.txt**：添加了 `chardet>=4.0.0` 依赖

### 编码处理日志

修复后的爬虫会输出详细的编码处理日志：
```
检测到编码: GB2312, 置信度: 0.99
使用 GB2312 编码成功解码
详情页检测到编码: GB2312, 置信度: 0.99
详情页使用 GB2312 编码成功解码
```

### 测试验证

通过测试脚本验证所有爬虫的编码处理功能：

- **LinkCollectorSpider**：✓ 成功提取25个电影链接
- **DyttSpider**：✓ 成功提取25个电影链接  
- **DetailCrawlerSpider**：✓ 成功提取电影详情（1884字符）

所有爬虫都能正确检测GB2312编码（置信度0.99），成功解码无乱码。

# 查看错误日志
tail -f logs/spider_error.log

# 查看特定时间段的日志
grep "2024-01-01" logs/spider.log
```

## 项目结构

```
dytt-spider/
├── dyttspider/
│   ├── dyttspider/
│   │   ├── __init__.py
│   │   ├── items.py                    # 数据项定义
│   │   ├── middlewares.py              # 中间件
│   │   ├── anti_ban_middlewares.py     # 防封IP中间件
│   │   ├── pipelines.py                # 数据处理管道
│   │   ├── pipelines_link.py           # 链接管道
│   │   ├── settings.py                 # 爬虫设置
│   │   ├── anti_ban_settings.py        # 防封IP配置
│   │   ├── utils/
│   │   │   ├── __init__.py
│   │   │   └── logger.py               # 日志工具模块
│   │   └── spiders/
│   │       ├── dytt_spider.py          # 原始爬虫（已集成编码处理）
│   │       ├── link_collector_spider.py # 链接收集爬虫（已集成编码处理）
│   │       └── detail_crawler_spider.py # 详情爬取爬虫（已集成编码处理）
│   └── scrapy.cfg
├── logs/                               # 日志文件目录
│   ├── spider.log                      # 主日志文件
│   └── spider_error.log                # 错误日志文件
├── requirements.txt                    # 依赖包列表（包含chardet）
├── run_link_collector.py              # 链接收集脚本
├── run_detail_crawler.py              # 详情爬取脚本
├── manage_movie_links.py              # 链接管理脚本
├── init_database_detailed.py          # 数据库初始化脚本
├── check_detailed_db.py               # 数据库检查脚本
├── clear_database.py                  # 数据库清理脚本
├── check_latest_movies.py             # 最新电影检查脚本
├── 防封IP完整指南.md                   # 防封IP完整指南（理论+实战）
└── README.md
```

## 防封IP策略

本项目集成了完整的防封IP机制，通过多层防护策略有效避免爬虫被目标网站封禁。

### 核心防护机制

1. **请求频率控制**
   - 智能延迟：根据响应状态动态调整请求间隔
   - 并发限制：控制同时进行的请求数量
   - 自动限速：基于响应时间自动调节爬取速度

2. **身份伪装**
   - 随机User-Agent：轮换14种常见浏览器标识
   - 随机请求头：模拟真实浏览器的HTTP请求头
   - 会话管理：维持Cookie状态，模拟用户会话

3. **IP轮换**
   - 代理池支持：支持HTTP/HTTPS/SOCKS5代理
   - 失败切换：自动检测并切换失败的代理
   - 健康监控：实时监控代理可用性

4. **智能重试**
   - 状态码识别：针对403、429等反爬虫状态码特殊处理
   - 指数退避：失败后逐渐增加重试间隔
   - 异常恢复：网络异常自动重试机制

### 配置说明

#### 基础配置

在 `anti_ban_settings.py` 中调整防封参数：

```python
# 并发控制（推荐保守设置）
CONCURRENT_REQUESTS = 4
CONCURRENT_REQUESTS_PER_DOMAIN = 1

# 延迟设置
DOWNLOAD_DELAY = 3  # 基础延迟3秒
RANDOMIZE_DOWNLOAD_DELAY = 0.5  # 随机因子

# 自动限速（强烈推荐）
AUTOTHROTTLE_ENABLED = True
AUTOTHROTTLE_START_DELAY = 3
AUTOTHROTTLE_MAX_DELAY = 30
```

#### 代理配置

```python
# 配置代理列表
PROXY_LIST = [
    'http://proxy1.example.com:8080',
    'http://username:<EMAIL>:8080',
    'socks5://proxy3.example.com:1080',
]
```

### 使用建议

1. **渐进式调优**
   - 从保守设置开始（大延迟、小并发）
   - 根据成功率逐步调整参数
   - 监控日志中的403、429状态码

2. **代理选择**
   - 优先使用住宅代理（成功率更高）
   - 配置多个代理实现轮换
   - 定期更新代理列表

3. **监控指标**
   - 成功率应保持在90%以上
   - 平均响应时间不应异常增长
   - 关注错误日志中的封禁信号

### 配置模板

**保守模式**（高成功率）：
```python
CONCURRENT_REQUESTS = 2
DOWNLOAD_DELAY = 5
```

**平衡模式**（推荐）：
```python
CONCURRENT_REQUESTS = 4
DOWNLOAD_DELAY = 3
```

**快速模式**（需谨慎使用）：
```python
CONCURRENT_REQUESTS = 8
DOWNLOAD_DELAY = 1
```

### 详细指南

更多详细的防封IP策略和实战技巧，请参考：[防封IP完整指南](防封IP完整指南.md)

## 许可证

MIT License