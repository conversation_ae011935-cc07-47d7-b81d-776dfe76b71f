#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查所有表的字段备注情况
"""

import pymysql

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'dytt2026',
    'charset': 'utf8mb4'
}

def check_table_comments(table_name):
    """检查指定表的字段备注"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print(f"\n📋 {table_name} 表字段备注情况:")
        print("-" * 80)
        
        # 获取表结构
        cursor.execute(f"SHOW CREATE TABLE {table_name}")
        result = cursor.fetchone()
        
        if result:
            create_sql = result[1]
            lines = create_sql.split('\n')
            
            # 统计有备注和无备注的字段
            total_fields = 0
            commented_fields = 0
            
            for line in lines:
                line = line.strip()
                if line.startswith('`') and ('int' in line.lower() or 'varchar' in line.lower() or 'text' in line.lower() or 'timestamp' in line.lower() or 'enum' in line.lower() or 'decimal' in line.lower() or 'date' in line.lower() or 'boolean' in line.lower()):
                    total_fields += 1
                    if 'COMMENT' in line:
                        commented_fields += 1
                        # 提取字段名和备注
                        field_name = line.split('`')[1]
                        comment_start = line.find("COMMENT '") + 9
                        comment_end = line.rfind("'")
                        if comment_start > 8 and comment_end > comment_start:
                            comment = line[comment_start:comment_end]
                            print(f"  ✅ {field_name:<20} - {comment}")
                        else:
                            print(f"  ✅ {field_name:<20} - 有备注")
                    else:
                        field_name = line.split('`')[1]
                        print(f"  ❌ {field_name:<20} - 无备注")
            
            # 获取表备注
            table_comment_start = create_sql.rfind("COMMENT='") + 9
            table_comment_end = create_sql.rfind("'")
            if table_comment_start > 8 and table_comment_end > table_comment_start:
                table_comment = create_sql[table_comment_start:table_comment_end]
                print(f"\n  📝 表备注: {table_comment}")
            else:
                print(f"\n  ❌ 表备注: 无")
            
            print(f"\n  📊 统计: {commented_fields}/{total_fields} 个字段有备注 ({commented_fields/total_fields*100:.1f}%)")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查表 {table_name} 失败: {e}")

def main():
    """检查所有表的备注情况"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("🔍 检查数据库中所有表的字段备注情况")
        print("=" * 80)
        
        # 获取所有表
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        # 检查每个表
        for table in tables:
            table_name = table[0]
            check_table_comments(table_name)
        
        print(f"\n🎉 检查完成！")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    main()
