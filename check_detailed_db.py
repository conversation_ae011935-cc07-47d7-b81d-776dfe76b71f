#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查详细数据库内容
查看规范化数据库结构中的数据
"""

import pymysql
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'dytt2026',
    'charset': 'utf8mb4'
}

def check_detailed_database():
    """检查详细数据库内容"""
    try:
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("🎬 详细电影数据库统计")
        print("=" * 60)
        
        # 1. 统计各表数据量
        tables = [
            ('movies', '电影基本信息'),
            ('genres', '电影类型'),
            ('movie_genres', '电影-类型关联'),
            ('persons', '人员信息'),
            ('movie_persons', '电影-人员关联'),
            ('download_links', '下载链接')
        ]
        
        print("📊 数据表统计:")
        for table_name, description in tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                count = cursor.fetchone()[0]
                print(f"   {description:<15} ({table_name:<15}): {count:>6} 条记录")
            except Exception as e:
                print(f"   {description:<15} ({table_name:<15}): 表不存在")
        
        print("\n" + "=" * 60)
        
        # 2. 显示最新的电影信息
        print("\n🎭 最新电影信息 (前5部):")
        movie_sql = """
        SELECT m.id, m.title, m.year, m.rating, m.language, m.country, m.created_at
        FROM movies m
        ORDER BY m.created_at DESC
        LIMIT 5
        """
        
        cursor.execute(movie_sql)
        movies = cursor.fetchall()
        
        for i, movie in enumerate(movies, 1):
            movie_id, title, year, rating, language, country, created_at = movie
            print(f"\n{i}. {title}")
            print(f"   年份: {year or '未知'} | 评分: {rating or '无'} | 语言: {language or '未知'}")
            print(f"   国家: {country or '未知'} | 录入时间: {created_at}")
            
            # 获取电影类型
            genre_sql = """
            SELECT g.name FROM genres g
            JOIN movie_genres mg ON g.id = mg.genre_id
            WHERE mg.movie_id = %s
            """
            cursor.execute(genre_sql, (movie_id,))
            genres = [row[0] for row in cursor.fetchall()]
            if genres:
                print(f"   类型: {' / '.join(genres)}")
            
            # 获取导演
            director_sql = """
            SELECT p.name FROM persons p
            JOIN movie_persons mp ON p.id = mp.person_id
            WHERE mp.movie_id = %s AND mp.role_type = 'director'
            """
            cursor.execute(director_sql, (movie_id,))
            directors = [row[0] for row in cursor.fetchall()]
            if directors:
                print(f"   导演: {' / '.join(directors)}")
            
            # 获取主演
            actor_sql = """
            SELECT p.name FROM persons p
            JOIN movie_persons mp ON p.id = mp.person_id
            WHERE mp.movie_id = %s AND mp.role_type = 'actor'
            ORDER BY mp.role_order
            LIMIT 3
            """
            cursor.execute(actor_sql, (movie_id,))
            actors = [row[0] for row in cursor.fetchall()]
            if actors:
                print(f"   主演: {' / '.join(actors)}")
            
            # 获取下载链接数量
            link_sql = "SELECT COUNT(*) FROM download_links WHERE movie_id = %s"
            cursor.execute(link_sql, (movie_id,))
            link_count = cursor.fetchone()[0]
            print(f"   下载链接: {link_count} 个")
        
        # 3. 统计分析
        print("\n" + "=" * 60)
        print("\n📈 数据分析:")
        
        # 年份分布
        year_sql = """
        SELECT year, COUNT(*) as count
        FROM movies
        WHERE year IS NOT NULL
        GROUP BY year
        ORDER BY year DESC
        LIMIT 10
        """
        cursor.execute(year_sql)
        years = cursor.fetchall()
        if years:
            print("\n   📅 年份分布 (前10年):")
            for year, count in years:
                print(f"      {year}: {count} 部")
        
        # 类型分布
        genre_sql = """
        SELECT g.name, COUNT(*) as count
        FROM genres g
        JOIN movie_genres mg ON g.id = mg.genre_id
        GROUP BY g.id, g.name
        ORDER BY count DESC
        LIMIT 10
        """
        cursor.execute(genre_sql)
        genres = cursor.fetchall()
        if genres:
            print("\n   🎭 类型分布 (前10类):")
            for genre, count in genres:
                print(f"      {genre}: {count} 部")
        
        # 评分分布
        rating_sql = """
        SELECT 
            CASE 
                WHEN rating >= 9 THEN '9.0+'
                WHEN rating >= 8 THEN '8.0-8.9'
                WHEN rating >= 7 THEN '7.0-7.9'
                WHEN rating >= 6 THEN '6.0-6.9'
                WHEN rating >= 5 THEN '5.0-5.9'
                ELSE '5.0以下'
            END as rating_range,
            COUNT(*) as count
        FROM movies
        WHERE rating IS NOT NULL
        GROUP BY rating_range
        ORDER BY rating_range DESC
        """
        cursor.execute(rating_sql)
        ratings = cursor.fetchall()
        if ratings:
            print("\n   ⭐ 评分分布:")
            for rating_range, count in ratings:
                print(f"      {rating_range}: {count} 部")
        
        # 下载链接类型分布
        link_type_sql = """
        SELECT link_type, COUNT(*) as count
        FROM download_links
        GROUP BY link_type
        ORDER BY count DESC
        """
        cursor.execute(link_type_sql)
        link_types = cursor.fetchall()
        if link_types:
            print("\n   🔗 下载链接类型:")
            for link_type, count in link_types:
                print(f"      {link_type}: {count} 个")
        
        conn.close()
        print("\n✅ 详细数据库检查完成")
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

def show_movie_detail(movie_id):
    """显示特定电影的详细信息"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 获取电影基本信息
        movie_sql = "SELECT * FROM movies WHERE id = %s"
        cursor.execute(movie_sql, (movie_id,))
        movie = cursor.fetchone()
        
        if not movie:
            print(f"❌ 未找到ID为 {movie_id} 的电影")
            return
        
        print(f"\n🎬 电影详细信息 (ID: {movie_id})")
        print("=" * 60)
        
        # 显示基本信息
        columns = ['id', 'title', 'original_title', 'url', 'year', 'duration', 
                  'rating', 'rating_count', 'language', 'country', 'release_date',
                  'publish_time', 'description', 'plot_summary', 'poster_url',
                  'imdb_id', 'douban_id', 'status', 'created_at', 'updated_at']
        
        movie_dict = dict(zip(columns, movie))
        
        print(f"标题: {movie_dict['title']}")
        if movie_dict['original_title']:
            print(f"原标题: {movie_dict['original_title']}")
        print(f"年份: {movie_dict['year'] or '未知'}")
        print(f"评分: {movie_dict['rating'] or '无'}")
        print(f"时长: {movie_dict['duration'] or '未知'} 分钟")
        print(f"语言: {movie_dict['language'] or '未知'}")
        print(f"国家: {movie_dict['country'] or '未知'}")
        print(f"链接: {movie_dict['url']}")
        
        if movie_dict['description']:
            print(f"\n描述: {movie_dict['description'][:200]}...")
        
        # 显示类型、人员、下载链接等信息...
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 获取电影详情失败: {e}")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1:
        try:
            movie_id = int(sys.argv[1])
            show_movie_detail(movie_id)
        except ValueError:
            print("❌ 请提供有效的电影ID")
    else:
        check_detailed_database()