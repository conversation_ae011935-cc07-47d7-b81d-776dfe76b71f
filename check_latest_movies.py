#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查网站最新电影列表
"""

import requests
from bs4 import BeautifulSoup
import time

def check_latest_movies():
    """检查网站最新电影列表"""
    url = "https://www.dytt8899.com/html/gndy/dyzz/"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        print(f"🔍 正在检查网站: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        response.encoding = 'gb2312'
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找电影列表
        movie_links = soup.select('table.tbspan a[href*="/i/"]')
        
        print(f"\n📋 找到 {len(movie_links)} 个电影链接")
        print("\n🎬 最新电影列表 (前20部):")
        print("=" * 80)
        
        for i, link in enumerate(movie_links[:20], 1):
            title = link.get_text(strip=True)
            href = link.get('href')
            full_url = f"https://www.dytt8899.com{href}" if href.startswith('/') else href
            
            print(f"{i:2d}. {title}")
            print(f"    链接: {full_url}")
            
            # 检查是否包含"会计刺客2"
            if "会计刺客2" in title or "会计刺客" in title:
                print(f"    ⭐ 找到目标电影！")
            
            print()
        
        # 专门搜索包含"会计刺客"的电影
        print("\n🔍 搜索包含'会计刺客'的电影:")
        print("=" * 50)
        
        found_target = False
        for i, link in enumerate(movie_links, 1):
            title = link.get_text(strip=True)
            if "会计刺客" in title:
                href = link.get('href')
                full_url = f"https://www.dytt8899.com{href}" if href.startswith('/') else href
                print(f"✅ 第{i}位: {title}")
                print(f"   链接: {full_url}")
                found_target = True
        
        if not found_target:
            print("❌ 未找到包含'会计刺客'的电影")
            
            # 检查是否有其他2025年的动作片
            print("\n🔍 搜索2025年动作片:")
            print("=" * 50)
            
            for i, link in enumerate(movie_links[:30], 1):
                title = link.get_text(strip=True)
                if "2025" in title and ("动作" in title or "Action" in title.lower()):
                    href = link.get('href')
                    full_url = f"https://www.dytt8899.com{href}" if href.startswith('/') else href
                    print(f"📽️ 第{i}位: {title}")
                    print(f"   链接: {full_url}")
                    print()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == '__main__':
    check_latest_movies()