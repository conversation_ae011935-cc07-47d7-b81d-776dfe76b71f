#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空数据库脚本
"""

import pymysql

def clear_database():
    """清空数据库中的所有数据"""
    try:
        # 连接数据库
        conn = pymysql.connect(
            host='127.0.0.1',
            port=3306,
            user='root',
            password='root',
            database='dytt2026',
            charset='utf8mb4'
        )
        cursor = conn.cursor()
        
        print("🗑️ 开始清空数据库...")
        
        # 清空详细数据库表
        tables = [
            'movie_persons',
            'persons', 
            'download_links',
            'movie_genres',
            'genres',
            'movies'
        ]
        
        for table in tables:
            try:
                cursor.execute(f"DELETE FROM {table}")
                print(f"✅ 已清空表: {table}")
            except Exception as e:
                print(f"⚠️ 清空表 {table} 失败: {e}")
        
        # 重置自增ID
        for table in tables:
            try:
                cursor.execute(f"ALTER TABLE {table} AUTO_INCREMENT = 1")
            except Exception as e:
                print(f"⚠️ 重置表 {table} 自增ID失败: {e}")
        
        conn.commit()
        print("\n🎉 数据库清空完成！")
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    clear_database()