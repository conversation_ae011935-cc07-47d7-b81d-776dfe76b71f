#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完成剩余表的字段备注更新
"""

import pymysql

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'dytt2026',
    'charset': 'utf8mb4'
}

def update_remaining_tables():
    """更新剩余表的字段备注"""
    
    # 定义各表需要更新的字段备注
    table_updates = {
        'movies': [
            "ALTER TABLE movies MODIFY COLUMN id INT AUTO_INCREMENT COMMENT '主键ID，电影唯一标识'"
        ],
        
        'persons': [
            "ALTER TABLE persons MODIFY COLUMN id INT AUTO_INCREMENT COMMENT '主键ID，人员唯一标识'"
        ],
        
        'genres': [
            "ALTER TABLE genres MODIFY COLUMN id INT AUTO_INCREMENT COMMENT '主键ID，类型唯一标识'"
        ],
        
        'download_links': [
            "ALTER TABLE download_links MODIFY COLUMN id INT AUTO_INCREMENT COMMENT '主键ID，下载链接唯一标识'",
            "ALTER TABLE download_links MODIFY COLUMN movie_id INT NOT NULL COMMENT '关联的电影ID，外键引用movies表'"
        ],
        
        'movie_persons': [
            "ALTER TABLE movie_persons MODIFY COLUMN id INT AUTO_INCREMENT COMMENT '主键ID，关联记录唯一标识'",
            "ALTER TABLE movie_persons MODIFY COLUMN movie_id INT NOT NULL COMMENT '关联的电影ID，外键引用movies表'",
            "ALTER TABLE movie_persons MODIFY COLUMN person_id INT NOT NULL COMMENT '关联的人员ID，外键引用persons表'"
        ],
        
        'movie_genres': [
            "ALTER TABLE movie_genres MODIFY COLUMN id INT AUTO_INCREMENT COMMENT '主键ID，关联记录唯一标识'",
            "ALTER TABLE movie_genres MODIFY COLUMN movie_id INT NOT NULL COMMENT '关联的电影ID，外键引用movies表'",
            "ALTER TABLE movie_genres MODIFY COLUMN genre_id INT NOT NULL COMMENT '关联的类型ID，外键引用genres表'"
        ]
    }
    
    try:
        print("🔄 连接数据库...")
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("✅ 数据库连接成功")
        print("\n🔄 开始更新剩余表的字段备注...")
        
        total_success = 0
        total_statements = 0
        
        for table_name, statements in table_updates.items():
            print(f"\n📝 更新 {table_name} 表:")
            
            for statement in statements:
                total_statements += 1
                try:
                    cursor.execute(statement)
                    total_success += 1
                    # 提取字段名
                    field_name = statement.split('COLUMN ')[1].split(' ')[0]
                    print(f"  ✅ {field_name} 字段备注更新成功")
                except Exception as e:
                    field_name = statement.split('COLUMN ')[1].split(' ')[0] if 'COLUMN' in statement else '未知字段'
                    print(f"  ❌ {field_name} 字段备注更新失败: {e}")
        
        connection.commit()
        
        print(f"\n🎉 字段备注更新完成！")
        print(f"📊 成功: {total_success}/{total_statements} 条语句")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")

def verify_updates():
    """验证更新结果"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("\n🔍 验证更新结果...")
        print("=" * 60)
        
        # 检查关键表的字段备注
        key_tables = ['movies', 'persons', 'genres', 'download_links', 'movie_persons', 'movie_genres']
        
        for table_name in key_tables:
            cursor.execute(f"SHOW CREATE TABLE {table_name}")
            result = cursor.fetchone()
            
            if result:
                create_sql = result[1]
                lines = create_sql.split('\n')
                
                print(f"\n📋 {table_name} 表关键字段:")
                
                # 查找 id 和外键字段
                for line in lines:
                    line = line.strip()
                    if (line.startswith('`id`') or 
                        line.startswith('`movie_id`') or 
                        line.startswith('`person_id`') or 
                        line.startswith('`genre_id`')):
                        
                        if 'COMMENT' in line:
                            field_name = line.split('`')[1]
                            comment_start = line.find("COMMENT '") + 9
                            comment_end = line.rfind("'")
                            if comment_start > 8 and comment_end > comment_start:
                                comment = line[comment_start:comment_end]
                                print(f"  ✅ {field_name:<12} - {comment}")
                        else:
                            field_name = line.split('`')[1]
                            print(f"  ❌ {field_name:<12} - 无备注")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='完成剩余表的字段备注更新')
    parser.add_argument('--update', action='store_true', help='更新剩余表的字段备注')
    parser.add_argument('--verify', action='store_true', help='验证更新结果')
    
    args = parser.parse_args()
    
    if args.update:
        update_remaining_tables()
        verify_updates()
    elif args.verify:
        verify_updates()
    else:
        print("使用方法:")
        print("  python complete_table_comments.py --update   # 更新剩余表的字段备注")
        print("  python complete_table_comments.py --verify   # 验证更新结果")
