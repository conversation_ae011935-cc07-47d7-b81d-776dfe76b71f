#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建失败列表页记录表
用于记录爬取失败的列表页URL，方便下次重试
"""

import pymysql
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'dytt2026',
    'charset': 'utf8mb4'
}

def create_failed_pages_table():
    """创建失败列表页记录表"""
    try:
        # 连接数据库
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 创建失败列表页表
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS failed_list_pages (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            url VARCHAR(500) NOT NULL COMMENT '失败的列表页URL',
            category VARCHAR(200) COMMENT '分类（最新电影/华语电影等）',
            failure_reason VARCHAR(500) COMMENT '失败原因',
            status_code VARCHAR(20) COMMENT 'HTTP状态码',
            retry_count INT DEFAULT 0 COMMENT '重试次数',
            last_attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后尝试时间',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            is_resolved BOOLEAN DEFAULT FALSE COMMENT '是否已解决',
            resolved_at TIMESTAMP NULL COMMENT '解决时间',
            notes TEXT COMMENT '备注信息',
            
            INDEX idx_url (url),
            INDEX idx_category (category),
            INDEX idx_retry_count (retry_count),
            INDEX idx_is_resolved (is_resolved),
            INDEX idx_last_attempt (last_attempt_time),
            UNIQUE KEY unique_url (url)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='失败列表页记录表';
        """
        
        cursor.execute(create_table_sql)
        print("✅ 失败列表页记录表 'failed_list_pages' 创建成功")
        
        # 显示表结构
        cursor.execute("DESCRIBE failed_list_pages")
        columns = cursor.fetchall()
        
        print("\n📋 表结构:")
        print(f"{'字段名':<20} {'类型':<25} {'是否为空':<10} {'键':<10} {'默认值':<15} {'备注':<20}")
        print("-" * 100)
        
        for column in columns:
            field, type_info, null, key, default, extra = column
            print(f"{field:<20} {type_info:<25} {null:<10} {key:<10} {str(default):<15} {extra:<20}")
        
        cursor.close()
        connection.close()
        
        print("\n🎯 表功能说明:")
        print("   - 记录爬取失败的列表页URL")
        print("   - 支持失败原因和状态码记录")
        print("   - 自动记录重试次数和时间")
        print("   - 支持标记已解决状态")
        print("   - URL唯一性约束，避免重复记录")
        
    except Exception as e:
        print(f"❌ 创建失败列表页记录表失败: {e}")
        raise

def show_table_info():
    """显示表信息"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM information_schema.tables 
            WHERE table_schema = 'dytt2026' 
            AND table_name = 'failed_list_pages'
        """)
        
        result = cursor.fetchone()
        if result[0] > 0:
            print("✅ failed_list_pages 表已存在")
            
            # 显示记录数量
            cursor.execute("SELECT COUNT(*) FROM failed_list_pages")
            count = cursor.fetchone()[0]
            print(f"📊 当前记录数: {count}")
            
            if count > 0:
                # 显示统计信息
                cursor.execute("""
                    SELECT 
                        category,
                        COUNT(*) as count,
                        AVG(retry_count) as avg_retry,
                        SUM(CASE WHEN is_resolved = 1 THEN 1 ELSE 0 END) as resolved_count
                    FROM failed_list_pages 
                    GROUP BY category
                """)
                
                stats = cursor.fetchall()
                print("\n📈 分类统计:")
                print(f"{'分类':<15} {'失败数':<10} {'平均重试':<12} {'已解决':<10}")
                print("-" * 50)
                for stat in stats:
                    category, count, avg_retry, resolved = stat
                    print(f"{category:<15} {count:<10} {avg_retry:<12.1f} {resolved:<10}")
        else:
            print("❌ failed_list_pages 表不存在")
            
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 查询表信息失败: {e}")

if __name__ == '__main__':
    print("🚀 开始创建失败列表页记录表...")
    create_failed_pages_table()
    print("\n" + "="*60)
    show_table_info()
    print("\n✨ 创建完成！现在可以修改爬虫代码来使用这个表了。")