#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接更新数据库表字段备注
"""

import pymysql

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'dytt2026',
    'charset': 'utf8mb4'
}

def update_movie_links_table():
    """更新 movie_links 表的字段备注"""
    
    alter_statements = [
        "ALTER TABLE movie_links MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID，用于唯一标识每个电影链接'",
        "ALTER TABLE movie_links MODIFY COLUMN title VARCHAR(500) COMMENT '电影标题'",
        "ALTER TABLE movie_links MODIFY COLUMN url VARCHAR(500) UNIQUE COMMENT '电影详情页链接，唯一约束'",
        "ALTER TABLE movie_links MODIFY COLUMN category VARCHAR(200) COMMENT '电影分类（最新电影/华语电影等）'",
        "ALTER TABLE movie_links MODIFY COLUMN list_page_url VARCHAR(500) COMMENT '来源列表页URL'",
        "ALTER TABLE movie_links MODIFY COLUMN crawl_time VARCHAR(100) COMMENT '爬取时间'",
        "ALTER TABLE movie_links MODIFY COLUMN status VARCHAR(20) DEFAULT 'pending' COMMENT '爬取状态：pending-待爬取，crawling-爬取中，completed-已完成，failed-失败'",
        "ALTER TABLE movie_links MODIFY COLUMN retry_count INT DEFAULT 0 COMMENT '重试次数，用于控制失败重试'",
        "ALTER TABLE movie_links MODIFY COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间'",
        "ALTER TABLE movie_links MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'"
    ]
    
    try:
        print("🔄 连接数据库...")
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("✅ 数据库连接成功")
        print("\n🔄 开始更新 movie_links 表字段备注...")
        
        for i, statement in enumerate(alter_statements, 1):
            try:
                print(f"  执行语句 {i}/{len(alter_statements)}...")
                cursor.execute(statement)
                print(f"  ✅ 成功")
            except Exception as e:
                print(f"  ❌ 失败: {e}")
        
        # 更新表备注
        try:
            cursor.execute("ALTER TABLE movie_links COMMENT='电影链接表，存储待爬取的电影详情页链接'")
            print("  ✅ 表备注更新成功")
        except Exception as e:
            print(f"  ❌ 表备注更新失败: {e}")
        
        connection.commit()
        print("\n🎉 movie_links 表字段备注更新完成！")
        
        # 验证更新结果
        print("\n📋 验证更新结果:")
        cursor.execute("SHOW CREATE TABLE movie_links")
        result = cursor.fetchone()
        if result:
            print(result[1])
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")

if __name__ == "__main__":
    update_movie_links_table()
