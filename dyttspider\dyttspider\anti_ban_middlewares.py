# -*- coding: utf-8 -*-
"""
反反爬虫中间件
包含多种防止IP被封禁的策略
"""

import random
import time
import logging
from scrapy import signals
from scrapy.downloadermiddlewares.retry import RetryMiddleware
from scrapy.utils.response import response_status_message
from scrapy.core.downloader.handlers.http11 import TunnelError
from twisted.internet import defer
from twisted.internet.error import TimeoutError, DNSLookupError, \
    ConnectionRefusedError, ConnectionDone, ConnectError, \
    ConnectionLost, TCPTimedOutError


class RandomUserAgentMiddleware:
    """
    随机用户代理中间件
    轮换不同的User-Agent来模拟不同的浏览器和设备
    """
    
    def __init__(self):
        # 常用的User-Agent列表
        self.user_agent_list = [
            # Chrome on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            
            # Firefox on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',
            
            # Chrome on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            
            # Safari on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15',
            
            # Chrome on Linux
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            
            # Mobile browsers
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        ]
    
    def process_request(self, request, spider):
        """随机选择User-Agent"""
        ua = random.choice(self.user_agent_list)
        request.headers['User-Agent'] = ua
        spider.logger.debug(f'使用User-Agent: {ua}')
        return None


class RandomHeadersMiddleware:
    """
    随机请求头中间件
    添加随机的HTTP请求头来模拟真实浏览器行为
    """
    
    def __init__(self):
        self.accept_list = [
            'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        ]
        
        self.accept_language_list = [
            'zh-CN,zh;q=0.9,en;q=0.8',
            'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'zh-CN,zh;q=0.9',
            'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        ]
        
        self.accept_encoding_list = [
            'gzip, deflate, br',
            'gzip, deflate',
            'gzip',
        ]
    
    def process_request(self, request, spider):
        """添加随机请求头"""
        request.headers['Accept'] = random.choice(self.accept_list)
        request.headers['Accept-Language'] = random.choice(self.accept_language_list)
        request.headers['Accept-Encoding'] = random.choice(self.accept_encoding_list)
        request.headers['Connection'] = 'keep-alive'
        request.headers['Upgrade-Insecure-Requests'] = '1'
        
        # 随机添加一些可选的请求头
        if random.random() < 0.5:
            request.headers['DNT'] = '1'
        
        if random.random() < 0.3:
            request.headers['Cache-Control'] = 'max-age=0'
        
        return None


class ProxyMiddleware:
    """
    代理IP中间件
    支持HTTP和HTTPS代理，可配置代理池
    """
    
    def __init__(self):
        # 代理列表 - 需要根据实际情况配置
        # 格式: '***********************************:proxy_port'
        # 或者: 'http://proxy_host:proxy_port'
        self.proxy_list = [
            # 示例代理，实际使用时需要替换为真实的代理
            # 'http://proxy1.example.com:8080',
            # 'http://proxy2.example.com:8080',
            # 'http://username:<EMAIL>:8080',
        ]
        
        # 失败的代理记录
        self.failed_proxies = set()
        
    @classmethod
    def from_crawler(cls, crawler):
        # 从settings中获取代理配置
        middleware = cls()
        proxy_list = crawler.settings.get('PROXY_LIST', [])
        if proxy_list:
            middleware.proxy_list = proxy_list
        return middleware
    
    def process_request(self, request, spider):
        """为请求设置代理"""
        if not self.proxy_list:
            return None
            
        # 过滤掉失败的代理
        available_proxies = [p for p in self.proxy_list if p not in self.failed_proxies]
        
        if not available_proxies:
            spider.logger.warning('所有代理都已失败，重置失败代理列表')
            self.failed_proxies.clear()
            available_proxies = self.proxy_list
        
        # 随机选择一个代理
        proxy = random.choice(available_proxies)
        request.meta['proxy'] = proxy
        spider.logger.debug(f'使用代理: {proxy}')
        
        return None
    
    def process_exception(self, request, exception, spider):
        """处理代理异常"""
        proxy = request.meta.get('proxy')
        if proxy:
            spider.logger.warning(f'代理 {proxy} 发生异常: {exception}')
            self.failed_proxies.add(proxy)
        return None


class DelayMiddleware:
    """
    智能延迟中间件
    根据响应状态动态调整请求延迟
    """
    
    def __init__(self):
        self.delay = 1  # 基础延迟时间（秒）
        self.max_delay = 10  # 最大延迟时间
        self.min_delay = 0.5  # 最小延迟时间
        self.last_request_time = 0
        
    @classmethod
    def from_crawler(cls, crawler):
        middleware = cls()
        settings = crawler.settings
        middleware.delay = settings.getfloat('DOWNLOAD_DELAY', 1)
        middleware.max_delay = settings.getfloat('MAX_DOWNLOAD_DELAY', 10)
        middleware.min_delay = settings.getfloat('MIN_DOWNLOAD_DELAY', 0.5)
        return middleware
    
    def process_request(self, request, spider):
        """在请求前添加延迟"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.delay:
            sleep_time = self.delay - time_since_last
            # 添加随机因子
            sleep_time += random.uniform(0, self.delay * 0.5)
            spider.logger.debug(f'延迟 {sleep_time:.2f} 秒')
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
        return None
    
    def process_response(self, request, response, spider):
        """根据响应状态调整延迟"""
        if response.status == 429:  # Too Many Requests
            self.delay = min(self.delay * 2, self.max_delay)
            spider.logger.warning(f'收到429状态码，增加延迟到 {self.delay} 秒')
        elif response.status in [403, 503]:  # Forbidden or Service Unavailable
            self.delay = min(self.delay * 1.5, self.max_delay)
            spider.logger.warning(f'收到{response.status}状态码，增加延迟到 {self.delay} 秒')
        elif response.status == 200:
            # 成功响应，逐渐减少延迟
            self.delay = max(self.delay * 0.95, self.min_delay)
        
        return response


class EnhancedRetryMiddleware(RetryMiddleware):
    """
    增强的重试中间件
    针对反爬虫场景优化的重试策略
    """
    
    EXCEPTIONS_TO_RETRY = (
        defer.TimeoutError, TimeoutError, DNSLookupError,
        ConnectionRefusedError, ConnectionDone, ConnectError,
        ConnectionLost, TCPTimedOutError, TunnelError
    )
    
    def __init__(self, settings):
        super().__init__(settings)
        self.retry_http_codes = set(int(x) for x in settings.getlist('RETRY_HTTP_CODES'))
        # 添加更多需要重试的状态码
        self.retry_http_codes.update([403, 429, 500, 502, 503, 504, 408])
        
    def process_response(self, request, response, spider):
        if request.meta.get('dont_retry', False):
            return response
            
        if response.status in self.retry_http_codes:
            reason = response_status_message(response.status)
            
            # 对于429状态码，增加更长的延迟
            if response.status == 429:
                retry_delay = random.uniform(5, 15)
                spider.logger.warning(f'收到429状态码，等待 {retry_delay:.2f} 秒后重试')
                time.sleep(retry_delay)
            
            # 对于403状态码，可能需要更换代理或User-Agent
            elif response.status == 403:
                spider.logger.warning('收到403状态码，可能被反爬虫检测')
                # 清除当前代理（如果有的话）
                if 'proxy' in request.meta:
                    del request.meta['proxy']
            
            return self._retry(request, reason, spider) or response
            
        return response
    
    def process_exception(self, request, exception, spider):
        if isinstance(exception, self.EXCEPTIONS_TO_RETRY) \
                and not request.meta.get('dont_retry', False):
            
            # 网络异常时增加延迟
            retry_delay = random.uniform(2, 8)
            spider.logger.warning(f'网络异常: {exception}，等待 {retry_delay:.2f} 秒后重试')
            time.sleep(retry_delay)
            
            return self._retry(request, exception, spider)


class SessionMiddleware:
    """
    会话管理中间件
    维持会话状态，模拟真实用户行为
    """
    
    def __init__(self):
        self.session_cookies = {}
        
    def process_request(self, request, spider):
        """添加会话Cookie"""
        domain = request.url.split('/')[2]
        if domain in self.session_cookies:
            for name, value in self.session_cookies[domain].items():
                request.cookies[name] = value
        return None
    
    def process_response(self, request, response, spider):
        """保存会话Cookie"""
        domain = request.url.split('/')[2]
        if 'Set-Cookie' in response.headers:
            if domain not in self.session_cookies:
                self.session_cookies[domain] = {}
            
            # 简单的Cookie解析（实际项目中可能需要更复杂的处理）
            cookies = response.headers.getlist('Set-Cookie')
            for cookie in cookies:
                if b'=' in cookie:
                    name, value = cookie.split(b'=', 1)
                    name = name.decode('utf-8').strip()
                    value = value.split(b';')[0].decode('utf-8').strip()
                    self.session_cookies[domain][name] = value
        
        return response