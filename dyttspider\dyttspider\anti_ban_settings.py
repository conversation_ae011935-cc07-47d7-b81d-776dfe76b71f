# -*- coding: utf-8 -*-
"""
防封IP配置文件
包含各种反反爬虫策略的配置参数
"""

# ==================== 基础爬虫设置 ====================

# 并发设置 - 降低并发数减少被检测的风险
CONCURRENT_REQUESTS = 4  # 全局并发请求数（默认16，建议4-8）
CONCURRENT_REQUESTS_PER_DOMAIN = 1  # 每个域名的并发数（建议1-2）

# 下载延迟设置
DOWNLOAD_DELAY = 3  # 基础延迟时间（秒）
RANDOMIZE_DOWNLOAD_DELAY = 0.5  # 随机延迟因子（0.5 * 到 1.5 * DOWNLOAD_DELAY）
MIN_DOWNLOAD_DELAY = 1  # 最小延迟时间
MAX_DOWNLOAD_DELAY = 10  # 最大延迟时间

# 自动限速设置（推荐启用）
AUTOTHROTTLE_ENABLED = True
AUTOTHROTTLE_START_DELAY = 3  # 初始延迟
AUTOTHROTTLE_MAX_DELAY = 30  # 最大延迟
AUTOTHROTTLE_TARGET_CONCURRENCY = 1.0  # 目标并发数
AUTOTHROTTLE_DEBUG = True  # 显示限速统计信息

# ==================== 重试设置 ====================

# 重试次数和状态码
RETRY_TIMES = 5  # 重试次数（默认2，建议3-5）
RETRY_HTTP_CODES = [500, 502, 503, 504, 408, 429, 403]  # 需要重试的HTTP状态码

# 重试延迟
RETRY_PRIORITY_ADJUST = -1  # 重试请求的优先级调整

# ==================== 代理设置 ====================

# 代理列表（需要根据实际情况配置）
PROXY_LIST = [
    # HTTP代理示例
    # 'http://proxy1.example.com:8080',
    # 'http://proxy2.example.com:8080',
    
    # 带认证的代理示例
    # 'http://username:<EMAIL>:8080',
    
    # SOCKS5代理示例
    # 'socks5://proxy4.example.com:1080',
]

# 代理轮换设置
PROXY_ROTATION_ENABLED = True  # 是否启用代理轮换
PROXY_FAIL_THRESHOLD = 3  # 代理失败阈值，超过此次数将被标记为不可用

# ==================== User-Agent设置 ====================

# 是否启用随机User-Agent
RANDOM_USER_AGENT_ENABLED = True

# 自定义User-Agent列表（可选，如果不设置将使用默认列表）
CUSTOM_USER_AGENT_LIST = [
    # 可以在这里添加特定的User-Agent
]

# ==================== 请求头设置 ====================

# 是否启用随机请求头
RANDOM_HEADERS_ENABLED = True

# 默认请求头
DEFAULT_REQUEST_HEADERS = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# ==================== Cookie设置 ====================

# Cookie设置
COOKIES_ENABLED = True  # 启用Cookie
COOKIES_DEBUG = False  # Cookie调试信息

# ==================== 缓存设置 ====================

# HTTP缓存（可以减少重复请求）
HTTPCACHE_ENABLED = False  # 是否启用缓存（调试时可启用）
HTTPCACHE_EXPIRATION_SECS = 3600  # 缓存过期时间（秒）
HTTPCACHE_DIR = 'httpcache'  # 缓存目录
HTTPCACHE_IGNORE_HTTP_CODES = [403, 404, 429, 500, 502, 503, 504]  # 不缓存的状态码

# ==================== 中间件配置 ====================

# 下载器中间件（按优先级排序，数字越小优先级越高）
DOWNLOADER_MIDDLEWARES = {
    # 内置中间件
    'scrapy.downloadermiddlewares.useragent.UserAgentMiddleware': None,  # 禁用默认UA中间件
    'scrapy.downloadermiddlewares.retry.RetryMiddleware': None,  # 禁用默认重试中间件
    
    # 自定义反反爬虫中间件
    'dyttspider.anti_ban_middlewares.RandomUserAgentMiddleware': 400,  # 随机UA
    'dyttspider.anti_ban_middlewares.RandomHeadersMiddleware': 410,  # 随机请求头
    'dyttspider.anti_ban_middlewares.ProxyMiddleware': 420,  # 代理中间件
    'dyttspider.anti_ban_middlewares.DelayMiddleware': 430,  # 智能延迟
    'dyttspider.anti_ban_middlewares.SessionMiddleware': 440,  # 会话管理
    'dyttspider.anti_ban_middlewares.EnhancedRetryMiddleware': 450,  # 增强重试
}

# ==================== 日志设置 ====================

# 日志级别（调试时可设为DEBUG）
LOG_LEVEL = 'INFO'

# 是否显示详细的调试信息
DEBUG_MODE = False

# ==================== 其他安全设置 ====================

# 遵守robots.txt（建议设为False以绕过限制）
ROBOTSTXT_OBEY = False

# DNS超时设置
DNS_TIMEOUT = 60

# 下载超时设置
DOWNLOAD_TIMEOUT = 180

# 下载器超时设置
DOWNLOADER_TIMEOUT = 180

# 重定向设置
REDIRECT_ENABLED = True
REDIRECT_MAX_TIMES = 5

# ==================== 高级设置 ====================

# 内存使用限制
MEMUSAGE_ENABLED = True
MEMUSAGE_LIMIT_MB = 2048  # 内存限制（MB）
MEMUSAGE_WARNING_MB = 1024  # 内存警告阈值（MB）

# 响应过滤
DUPEFILTER_DEBUG = False  # 重复过滤器调试

# 统计收集
STATS_CLASS = 'scrapy.statscollectors.MemoryStatsCollector'

# ==================== 使用说明 ====================
"""
使用方法：

1. 在settings.py中导入此配置：
   from .anti_ban_settings import *

2. 或者选择性导入特定配置：
   from .anti_ban_settings import DOWNLOADER_MIDDLEWARES, CONCURRENT_REQUESTS

3. 根据目标网站调整参数：
   - 对于严格的网站：增加延迟时间，减少并发数
   - 对于宽松的网站：可以适当提高爬取速度
   - 配置代理列表以避免IP封禁

4. 监控日志输出：
   - 观察429、403等状态码的出现频率
   - 根据响应时间调整延迟设置
   - 监控代理的成功率

5. 测试建议：
   - 先用较保守的设置进行测试
   - 逐步调整参数找到最佳平衡点
   - 定期检查爬虫的成功率和速度
"""