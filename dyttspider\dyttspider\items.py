# Define here the models for your scraped items
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/items.html

import scrapy


class MovieItem(scrapy.Item):
    # 基本信息
    title = scrapy.Field()  # 电影标题
    original_title = scrapy.Field()  # 原始标题
    translated_title = scrapy.Field()  # 译名
    url = scrapy.Field()  # 电影链接
    year = scrapy.Field()  # 电影年份
    duration = scrapy.Field()  # 电影时长
    rating = scrapy.Field()  # 电影评分
    rating_count = scrapy.Field()  # 评分人数
    language = scrapy.Field()  # 电影语言
    country = scrapy.Field()  # 制片国家/地区
    release_date = scrapy.Field()  # 上映日期
    publish_time = scrapy.Field()  # 发布时间
    description = scrapy.Field()  # 电影描述
    plot_summary = scrapy.Field()  # 剧情简介
    poster_url = scrapy.Field()  # 海报链接
    poster_local_path = scrapy.Field()  # 海报本地路径
    imdb_id = scrapy.Field()  # IMDB ID
    douban_id = scrapy.Field()  # 豆瓣ID
    
    # 分类信息
    genres = scrapy.Field()  # 电影类型列表
    
    # 人员信息
    directors = scrapy.Field()  # 导演列表
    actors = scrapy.Field()  # 演员列表
    writers = scrapy.Field()  # 编剧列表
    producers = scrapy.Field()  # 制片人列表
    
    # 下载信息
    download_links = scrapy.Field()  # 下载链接列表
    
    # 兼容旧字段
    category = scrapy.Field()  # 兼容旧的分类字段
    director = scrapy.Field()  # 兼容旧的导演字段


class PersonItem(scrapy.Item):
    """人员信息Item"""
    name = scrapy.Field()  # 姓名
    name_en = scrapy.Field()  # 英文名
    gender = scrapy.Field()  # 性别
    birth_date = scrapy.Field()  # 出生日期
    birth_place = scrapy.Field()  # 出生地
    nationality = scrapy.Field()  # 国籍
    biography = scrapy.Field()  # 个人简介
    photo_url = scrapy.Field()  # 照片链接
    imdb_id = scrapy.Field()  # IMDB ID
    douban_id = scrapy.Field()  # 豆瓣ID


class DownloadLinkItem(scrapy.Item):
    """下载链接Item"""
    link_type = scrapy.Field()  # 链接类型
    url = scrapy.Field()  # 下载链接
    file_name = scrapy.Field()  # 文件名
    file_size = scrapy.Field()  # 文件大小
    quality = scrapy.Field()  # 画质
    format = scrapy.Field()  # 格式
    language = scrapy.Field()  # 语言版本
    subtitle = scrapy.Field()  # 字幕信息
    seeds = scrapy.Field()  # 种子数
    peers = scrapy.Field()  # 下载者数


class GenreItem(scrapy.Item):
    """电影类型Item"""
    name = scrapy.Field()  # 类型名称
    name_en = scrapy.Field()  # 英文名称
    description = scrapy.Field()  # 类型描述


class MovieLinkItem(scrapy.Item):
    """电影链接Item - 用于第一阶段收集电影详情页链接"""
    title = scrapy.Field()  # 电影标题
    url = scrapy.Field()  # 电影详情页链接
    category = scrapy.Field()  # 电影分类
    list_page_url = scrapy.Field()  # 来源列表页URL
    crawl_time = scrapy.Field()  # 爬取时间
    status = scrapy.Field()  # 状态：pending, crawling, completed, failed
    retry_count = scrapy.Field()  # 重试次数
