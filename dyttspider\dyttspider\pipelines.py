# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html

import json
import os
import pymysql
from itemadapter import ItemAdapter
from dyttspider.items import MovieItem
from dyttspider.pipelines_link import MovieLinkPipeline


class MoviePipeline:
    def __init__(self):
        self.file = None
        self.movies = []
    
    def open_spider(self, spider):
        """爬虫开始时创建输出文件"""
        # 使用绝对路径确保文件创建在项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        movies_json_path = os.path.join(project_root, 'movies.json')
        self.file = open(movies_json_path, 'w', encoding='utf-8')
        
    def close_spider(self, spider):
        """爬虫结束时保存数据并关闭文件"""
        json.dump(self.movies, self.file, ensure_ascii=False, indent=2)
        self.file.close()
        print(f"\n爬取完成！共获取 {len(self.movies)} 部电影信息")
        print(f"数据已保存到 movies.json 文件")
    
    def process_item(self, item, spider):
        """处理每个电影项目"""
        if isinstance(item, MovieItem):
            adapter = ItemAdapter(item)
            
            # 数据清洗
            movie_data = {
                'title': self.clean_text(adapter.get('title', '')),
                'url': adapter.get('url', ''),
                'publish_time': self.clean_text(adapter.get('publish_time', '')),
                'category': self.clean_text(adapter.get('category', '')),
                'description': self.clean_text(adapter.get('description', '')),
                'download_links': adapter.get('download_links', []),
                'rating': adapter.get('rating', ''),
                'year': adapter.get('year', ''),
                'director': self.clean_text(adapter.get('director', '')),
                'actors': self.clean_text(adapter.get('actors', '')),
                'poster_url': adapter.get('poster_url', ''),
                'poster_local_path': adapter.get('poster_local_path', '')
            }
            
            self.movies.append(movie_data)
            print(f"已爬取电影: {movie_data['title']}")
            
        return item
    
    def clean_text(self, text):
        """清理文本数据"""
        if not text:
            return ''
        # 移除多余的空白字符
        text = ' '.join(text.split())
        # 移除特殊字符
        text = text.replace('\xa0', ' ').replace('\u3000', ' ')
        return text.strip()


class MySQLPipeline:
    def __init__(self, mysql_host, mysql_port, mysql_user, mysql_password, mysql_db):
        self.mysql_host = mysql_host
        self.mysql_port = mysql_port
        self.mysql_user = mysql_user
        self.mysql_password = mysql_password
        self.mysql_db = mysql_db
        self.connection = None
        self.cursor = None

    @classmethod
    def from_crawler(cls, crawler):
        db_settings = crawler.settings.getdict("DATABASE")
        return cls(
            mysql_host=db_settings['host'],
            mysql_port=db_settings['port'],
            mysql_user=db_settings['user'],
            mysql_password=db_settings['password'],
            mysql_db=db_settings['name']
        )

    def open_spider(self, spider):
        """爬虫开始时连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.mysql_host,
                port=self.mysql_port,
                user=self.mysql_user,
                password=self.mysql_password,
                database=self.mysql_db,
                charset='utf8mb4',
                autocommit=True
            )
            self.cursor = self.connection.cursor()
            
            # 创建电影表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS movies (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID，电影唯一标识',
                title VARCHAR(500) NOT NULL COMMENT '电影标题',
                url VARCHAR(500) COMMENT '电影详情页链接',
                publish_time VARCHAR(100) COMMENT '发布时间',
                category VARCHAR(200) COMMENT '电影分类',
                description TEXT COMMENT '电影描述/简介',
                download_links JSON COMMENT '下载链接JSON数据',
                rating VARCHAR(50) COMMENT '电影评分',
                year VARCHAR(10) COMMENT '电影年份',
                director VARCHAR(500) COMMENT '导演信息',
                actors TEXT COMMENT '演员信息',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                UNIQUE KEY unique_url (url)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电影信息表（简化版）';
            """
            
            self.cursor.execute(create_table_sql)
            spider.logger.info("MySQL数据库连接成功，电影表已创建")
            
        except Exception as e:
            spider.logger.error(f"MySQL数据库连接失败: {e}")
            raise

    def close_spider(self, spider):
        """爬虫结束时关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        spider.logger.info("MySQL数据库连接已关闭")

    def process_item(self, item, spider):
        """处理每个电影项目并存储到数据库"""
        if isinstance(item, MovieItem):
            adapter = ItemAdapter(item)
            
            # 数据清洗
            movie_data = {
                'title': self.clean_text(adapter.get('title', '')),
                'url': adapter.get('url', ''),
                'publish_time': self.clean_text(adapter.get('publish_time', '')),
                'category': self.clean_text(adapter.get('category', '')),
                'description': self.clean_text(adapter.get('description', '')),
                'download_links': json.dumps(adapter.get('download_links', []), ensure_ascii=False),
                'rating': adapter.get('rating', ''),
                'year': adapter.get('year', ''),
                'director': self.clean_text(adapter.get('director', '')),
                'actors': self.clean_text(adapter.get('actors', '')),
                'poster_url': adapter.get('poster_url', ''),
                'poster_local_path': adapter.get('poster_local_path', '')
            }
            
            try:
                # 插入数据到MySQL
                insert_sql = """
                INSERT INTO movies (title, url, publish_time, category, description, 
                                  download_links, rating, year, director, actors, poster_url, poster_local_path)
                VALUES (%(title)s, %(url)s, %(publish_time)s, %(category)s, %(description)s,
                       %(download_links)s, %(rating)s, %(year)s, %(director)s, %(actors)s, %(poster_url)s, %(poster_local_path)s)
                ON DUPLICATE KEY UPDATE
                    title = VALUES(title),
                    publish_time = VALUES(publish_time),
                    category = VALUES(category),
                    description = VALUES(description),
                    download_links = VALUES(download_links),
                    rating = VALUES(rating),
                    year = VALUES(year),
                    director = VALUES(director),
                    actors = VALUES(actors),
                    poster_url = VALUES(poster_url),
                    poster_local_path = VALUES(poster_local_path),
                    updated_at = CURRENT_TIMESTAMP
                """
                
                self.cursor.execute(insert_sql, movie_data)
                spider.logger.info(f"电影数据已保存到MySQL: {movie_data['title']}")
                
            except Exception as e:
                spider.logger.error(f"保存电影数据到MySQL失败: {e}")
                spider.logger.error(f"电影数据: {movie_data}")
                
        return item
    
    def clean_text(self, text):
        """清理文本数据"""
        if not text:
            return ''
        # 移除多余的空白字符
        text = ' '.join(text.split())
        # 移除特殊字符
        text = text.replace('\xa0', ' ').replace('\u3000', ' ')
        return text.strip()


class DyttspiderPipeline:
    def process_item(self, item, spider):
        return item
