# -*- coding: utf-8 -*-
"""
详细数据管道
处理规范化的电影数据库结构
"""

import json
import re
import pymysql
from itemadapter import ItemAdapter
from scrapy.exceptions import DropItem
import logging
from datetime import datetime


class DetailedMoviePipeline:
    """详细电影数据管道 - 处理规范化数据库结构"""
    
    def __init__(self, mysql_host, mysql_port, mysql_user, mysql_password, mysql_db):
        self.mysql_host = mysql_host
        self.mysql_port = mysql_port
        self.mysql_user = mysql_user
        self.mysql_password = mysql_password
        self.mysql_db = mysql_db
        self.connection = None
        self.cursor = None
    
    @classmethod
    def from_crawler(cls, crawler):
        """从爬虫设置中获取数据库配置"""
        return cls(
            mysql_host=crawler.settings.get('MYSQL_HOST', '127.0.0.1'),
            mysql_port=crawler.settings.get('MYSQL_PORT', 3306),
            mysql_user=crawler.settings.get('MYSQL_USER', 'root'),
            mysql_password=crawler.settings.get('MYSQL_PASSWORD', 'root'),
            mysql_db=crawler.settings.get('MYSQL_DATABASE', 'dytt2026')
        )
    
    def open_spider(self, spider):
        """爬虫启动时连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.mysql_host,
                port=self.mysql_port,
                user=self.mysql_user,
                password=self.mysql_password,
                database=self.mysql_db,
                charset='utf8mb4',
                autocommit=True
            )
            self.cursor = self.connection.cursor()
            spider.logger.info(f"MySQL数据库连接成功: {self.mysql_host}:{self.mysql_port}/{self.mysql_db}")
        except Exception as e:
            spider.logger.error(f"MySQL数据库连接失败: {e}")
            raise
    
    def close_spider(self, spider):
        """爬虫关闭时断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        spider.logger.info("MySQL数据库连接已关闭")
    
    def process_item(self, item, spider):
        """处理电影数据项"""
        try:
            adapter = ItemAdapter(item)
            
            # 1. 处理电影基本信息
            movie_id = self._process_movie_basic_info(adapter, spider)
            
            if movie_id:
                # 2. 处理电影类型
                self._process_movie_genres(movie_id, adapter, spider)
                
                # 3. 处理人员信息
                self._process_movie_persons(movie_id, adapter, spider)
                
                # 4. 处理下载链接
                self._process_download_links(movie_id, adapter, spider)
                
                spider.logger.info(f"电影数据处理完成: {adapter.get('title')}")
            
            return item
            
        except Exception as e:
            spider.logger.error(f"处理电影数据失败: {e}")
            raise DropItem(f"处理电影数据失败: {e}")
    
    def _process_movie_basic_info(self, adapter, spider):
        """处理电影基本信息"""
        try:
            # 清理和提取数据
            title = self._clean_text(adapter.get('title', ''))
            url = adapter.get('url', '')
            year = self._extract_year(adapter.get('year', ''))
            rating = self._extract_rating(adapter.get('rating', ''))
            description = self._clean_text(adapter.get('description', ''))
            
            # 从描述中提取更多信息
            extracted_info = self._extract_movie_info_from_description(description)
            
            # 检查电影是否已存在
            check_sql = "SELECT id FROM movies WHERE url = %s"
            self.cursor.execute(check_sql, (url,))
            existing_movie = self.cursor.fetchone()
            
            if existing_movie:
                movie_id = existing_movie[0]
                # 更新现有电影信息
                update_sql = """
                UPDATE movies SET 
                    title = %s, year = %s, rating = %s, description = %s,
                    language = %s, country = %s, duration = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
                """
                self.cursor.execute(update_sql, (
                    title, year, rating, description,
                    extracted_info.get('language'),
                    extracted_info.get('country'),
                    extracted_info.get('duration'),
                    movie_id
                ))
                spider.logger.info(f"更新电影信息: {title}")
            else:
                # 插入新电影
                insert_sql = """
                INSERT INTO movies (
                    title, url, year, rating, description, language, country, duration,
                    publish_time, status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                publish_time = self._parse_publish_time(adapter.get('publish_time', ''))
                
                self.cursor.execute(insert_sql, (
                    title, url, year, rating, description,
                    extracted_info.get('language'),
                    extracted_info.get('country'),
                    extracted_info.get('duration'),
                    publish_time,
                    'active'
                ))
                
                movie_id = self.cursor.lastrowid
                spider.logger.info(f"插入新电影: {title} (ID: {movie_id})")
            
            return movie_id
            
        except Exception as e:
            spider.logger.error(f"处理电影基本信息失败: {e}")
            return None
    
    def _process_movie_genres(self, movie_id, adapter, spider):
        """处理电影类型"""
        try:
            # 从category或genres字段获取类型信息
            genres_text = adapter.get('category', '') or adapter.get('genres', '')
            if not genres_text:
                return
            
            # 解析类型
            genres = self._parse_genres(genres_text)
            
            # 删除现有的类型关联
            delete_sql = "DELETE FROM movie_genres WHERE movie_id = %s"
            self.cursor.execute(delete_sql, (movie_id,))
            
            # 插入新的类型关联
            for genre_name in genres:
                genre_id = self._get_or_create_genre(genre_name)
                if genre_id:
                    insert_sql = "INSERT IGNORE INTO movie_genres (movie_id, genre_id) VALUES (%s, %s)"
                    self.cursor.execute(insert_sql, (movie_id, genre_id))
            
            spider.logger.debug(f"处理电影类型完成: {genres}")
            
        except Exception as e:
            spider.logger.error(f"处理电影类型失败: {e}")
    
    def _process_movie_persons(self, movie_id, adapter, spider):
        """处理电影人员信息"""
        try:
            # 删除现有的人员关联
            delete_sql = "DELETE FROM movie_persons WHERE movie_id = %s"
            self.cursor.execute(delete_sql, (movie_id,))
            
            # 处理导演
            directors = self._parse_persons(adapter.get('director', '') or adapter.get('directors', ''))
            for director_name in directors:
                person_id = self._get_or_create_person(director_name)
                if person_id:
                    self._link_movie_person(movie_id, person_id, 'director')
            
            # 处理演员
            actors = self._parse_persons(adapter.get('actors', ''))
            for i, actor_name in enumerate(actors):
                person_id = self._get_or_create_person(actor_name)
                if person_id:
                    self._link_movie_person(movie_id, person_id, 'actor', role_order=i+1)
            
            spider.logger.debug(f"处理电影人员完成: 导演{len(directors)}人, 演员{len(actors)}人")
            
        except Exception as e:
            spider.logger.error(f"处理电影人员失败: {e}")
    
    def _process_download_links(self, movie_id, adapter, spider):
        """处理下载链接"""
        try:
            download_links = adapter.get('download_links', [])
            if not download_links:
                return
            
            # 删除现有的下载链接
            delete_sql = "DELETE FROM download_links WHERE movie_id = %s"
            self.cursor.execute(delete_sql, (movie_id,))
            
            # 插入新的下载链接
            for link in download_links:
                if isinstance(link, str):
                    link_info = self._parse_download_link(link)
                    insert_sql = """
                    INSERT INTO download_links (
                        movie_id, link_type, url, file_name, quality, 
                        format, language, subtitle
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    
                    self.cursor.execute(insert_sql, (
                        movie_id,
                        link_info['type'],
                        link,
                        link_info['file_name'],
                        link_info['quality'],
                        link_info['format'],
                        link_info['language'],
                        link_info['subtitle']
                    ))
            
            spider.logger.debug(f"处理下载链接完成: {len(download_links)}个链接")
            
        except Exception as e:
            spider.logger.error(f"处理下载链接失败: {e}")
    
    def _get_or_create_genre(self, genre_name):
        """获取或创建电影类型"""
        try:
            # 查找现有类型
            select_sql = "SELECT id FROM genres WHERE name = %s"
            self.cursor.execute(select_sql, (genre_name,))
            result = self.cursor.fetchone()
            
            if result:
                return result[0]
            
            # 创建新类型
            insert_sql = "INSERT INTO genres (name) VALUES (%s)"
            self.cursor.execute(insert_sql, (genre_name,))
            return self.cursor.lastrowid
            
        except Exception as e:
            logging.error(f"获取或创建类型失败: {e}")
            return None
    
    def _get_or_create_person(self, person_name):
        """获取或创建人员"""
        try:
            if not person_name or person_name.strip() == '':
                return None
            
            person_name = person_name.strip()
            
            # 查找现有人员
            select_sql = "SELECT id FROM persons WHERE name = %s"
            self.cursor.execute(select_sql, (person_name,))
            result = self.cursor.fetchone()
            
            if result:
                return result[0]
            
            # 创建新人员
            insert_sql = "INSERT INTO persons (name) VALUES (%s)"
            self.cursor.execute(insert_sql, (person_name,))
            return self.cursor.lastrowid
            
        except Exception as e:
            logging.error(f"获取或创建人员失败: {e}")
            return None
    
    def _link_movie_person(self, movie_id, person_id, role_type, character_name=None, role_order=0):
        """关联电影和人员"""
        try:
            insert_sql = """
            INSERT INTO movie_persons (movie_id, person_id, role_type, character_name, role_order)
            VALUES (%s, %s, %s, %s, %s)
            """
            self.cursor.execute(insert_sql, (movie_id, person_id, role_type, character_name, role_order))
        except Exception as e:
            logging.error(f"关联电影人员失败: {e}")
    
    def _extract_movie_info_from_description(self, description):
        """从描述中提取电影信息"""
        info = {}
        
        if not description:
            return info
        
        # 提取语言
        language_patterns = [
            r'◎语\s*言\s*([^◎\n]+)',
            r'语言[：:]\s*([^\n]+)',
        ]
        for pattern in language_patterns:
            match = re.search(pattern, description)
            if match:
                info['language'] = match.group(1).strip()
                break
        
        # 提取国家
        country_patterns = [
            r'◎产\s*地\s*([^◎\n]+)',
            r'◎国\s*家\s*([^◎\n]+)',
            r'国家[：:]\s*([^\n]+)',
        ]
        for pattern in country_patterns:
            match = re.search(pattern, description)
            if match:
                info['country'] = match.group(1).strip()
                break
        
        # 提取时长
        duration_patterns = [
            r'◎片\s*长\s*([^◎\n]+)',
            r'时长[：:]\s*([^\n]+)',
        ]
        for pattern in duration_patterns:
            match = re.search(pattern, description)
            if match:
                duration_text = match.group(1).strip()
                # 提取数字
                duration_match = re.search(r'(\d+)', duration_text)
                if duration_match:
                    info['duration'] = int(duration_match.group(1))
                break
        
        return info
    
    def _parse_genres(self, genres_text):
        """解析电影类型"""
        if not genres_text:
            return []
        
        # 从描述中提取类型信息
        genre_patterns = [
            r'◎类\s*别\s*([^◎\n]+)',
            r'类型[：:]\s*([^\n]+)',
            r'类别[：:]\s*([^\n]+)'
        ]
        
        extracted_genres = []
        for pattern in genre_patterns:
            match = re.search(pattern, genres_text)
            if match:
                genre_text = match.group(1).strip()
                # 分割类型
                genres = re.split(r'[/、,，\s]+', genre_text)
                extracted_genres.extend([g.strip() for g in genres if g.strip()])
        
        # 如果没有从描述中提取到，尝试直接解析
        if not extracted_genres:
            genres = re.split(r'[/、,，\s]+', genres_text)
            extracted_genres = [g.strip() for g in genres if g.strip()]
        
        # 标准化和过滤类型
        valid_genres = []
        genre_mapping = {
            '动作': '动作', '喜剧': '喜剧', '剧情': '剧情', '爱情': '爱情',
            '科幻': '科幻', '惊悚': '惊悚', '恐怖': '恐怖', '悬疑': '悬疑',
            '犯罪': '犯罪', '冒险': '冒险', '奇幻': '奇幻', '战争': '战争',
            '历史': '历史', '传记': '传记', '音乐': '音乐', '家庭': '家庭',
            '动画': '动画', '纪录片': '纪录片', '短片': '短片', '西部': '西部',
            '运动': '运动', '歌舞': '歌舞'
        }
        
        for genre in extracted_genres:
            genre = genre.strip()
            # 过滤无效类型
            if (len(genre) >= 2 and len(genre) <= 10 and 
                not re.search(r'[◎\d]', genre) and
                genre not in ['片', '名', '地', '别', '代', '言', '幕']):
                
                # 标准化类型名称
                standardized = genre_mapping.get(genre, genre)
                if standardized not in valid_genres:
                    valid_genres.append(standardized)
        
        return valid_genres[:5]  # 最多返回5个类型
    
    def _parse_persons(self, persons_text):
        """解析人员信息"""
        if not persons_text:
            return []
        
        # 常见的分隔符
        separators = ['/', '、', ',', '，', ' ', '|']
        persons = [persons_text]
        
        for sep in separators:
            new_persons = []
            for person in persons:
                new_persons.extend([p.strip() for p in person.split(sep) if p.strip()])
            persons = new_persons
        
        # 过滤
        filtered_persons = []
        for person in persons:
            person = person.strip()
            if person and len(person) < 50 and not re.search(r'[◎\d]', person):
                filtered_persons.append(person)
        
        return filtered_persons
    
    def _parse_download_link(self, link):
        """解析下载链接信息"""
        info = {
            'type': 'magnet',
            'file_name': '',
            'quality': '',
            'format': '',
            'language': '',
            'subtitle': ''
        }
        
        if link.startswith('magnet:'):
            info['type'] = 'magnet'
            # 从磁力链接中提取文件名
            dn_match = re.search(r'dn=([^&]+)', link)
            if dn_match:
                import urllib.parse
                info['file_name'] = urllib.parse.unquote(dn_match.group(1))
        elif link.startswith('ed2k:'):
            info['type'] = 'ed2k'
        elif link.startswith('http'):
            info['type'] = 'http'
        
        # 从文件名中提取质量、格式等信息
        file_name = info['file_name'].lower()
        
        # 质量
        if 'hd' in file_name or '720p' in file_name:
            info['quality'] = 'HD'
        elif 'bd' in file_name or '1080p' in file_name:
            info['quality'] = 'BD'
        elif '4k' in file_name or '2160p' in file_name:
            info['quality'] = '4K'
        
        # 格式
        if '.mp4' in file_name:
            info['format'] = 'MP4'
        elif '.mkv' in file_name:
            info['format'] = 'MKV'
        elif '.avi' in file_name:
            info['format'] = 'AVI'
        
        # 语言和字幕
        if '中字' in file_name or '中文' in file_name:
            info['subtitle'] = '中文字幕'
        if '双语' in file_name:
            info['language'] = '双语'
        elif '国语' in file_name:
            info['language'] = '国语'
        elif '英语' in file_name:
            info['language'] = '英语'
        
        return info
    
    def _clean_text(self, text):
        """清理文本"""
        if not text:
            return ''
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', str(text))
        return text.strip()
    
    def _extract_year(self, year_text):
        """提取年份"""
        if not year_text:
            return None
        
        # 提取4位数字年份
        match = re.search(r'(19|20)\d{2}', str(year_text))
        if match:
            return int(match.group())
        
        return None
    
    def _extract_rating(self, rating_text):
        """提取评分"""
        if not rating_text:
            return None
        
        # 提取数字评分
        match = re.search(r'(\d+\.?\d*)', str(rating_text))
        if match:
            try:
                rating = float(match.group())
                if 0 <= rating <= 10:
                    return rating
            except ValueError:
                pass
        
        return None
    
    def _parse_publish_time(self, publish_time_text):
        """解析发布时间"""
        if not publish_time_text:
            return None
        
        try:
            # 尝试解析常见的时间格式
            time_formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%m/%d/%Y'
            ]
            
            for fmt in time_formats:
                try:
                    return datetime.strptime(str(publish_time_text).strip(), fmt)
                except ValueError:
                    continue
        except:
            pass
        
        return None