import scrapy
import os
import hashlib
import requests
from urllib.parse import urlparse
from scrapy.pipelines.images import ImagesPipeline
from scrapy.exceptions import DropItem
import logging

class MovieImagePipeline:
    """电影海报图片下载管道"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # 创建图片存储目录
        self.images_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'images')
        if not os.path.exists(self.images_dir):
            os.makedirs(self.images_dir)
            self.logger.info(f"创建图片目录: {self.images_dir}")
    
    def process_item(self, item, spider):
        """处理电影项目，下载海报图片"""
        if 'poster_url' in item and item['poster_url']:
            poster_url = item['poster_url']
            try:
                # 下载图片
                image_path = self.download_image(poster_url, item.get('title', 'unknown'))
                if image_path:
                    item['poster_local_path'] = image_path
                    self.logger.info(f"海报下载成功: {image_path}")
                else:
                    self.logger.warning(f"海报下载失败: {poster_url}")
                    item['poster_local_path'] = ''
            except Exception as e:
                self.logger.error(f"下载海报时出错 {poster_url}: {e}")
                item['poster_local_path'] = ''
        else:
            item['poster_local_path'] = ''
        
        return item
    
    def download_image(self, image_url, movie_title):
        """下载图片并保存到本地"""
        try:
            # 生成文件名
            parsed_url = urlparse(image_url)
            file_extension = os.path.splitext(parsed_url.path)[1] or '.jpg'
            
            # 使用电影标题和URL的哈希值生成唯一文件名
            url_hash = hashlib.md5(image_url.encode()).hexdigest()[:8]
            safe_title = "".join(c for c in movie_title if c.isalnum() or c in (' ', '-', '_')).rstrip()[:50]
            filename = f"{safe_title}_{url_hash}{file_extension}"
            
            file_path = os.path.join(self.images_dir, filename)
            
            # 如果文件已存在，直接返回路径
            if os.path.exists(file_path):
                return file_path
            
            # 下载图片
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(image_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 保存图片
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            return file_path
            
        except Exception as e:
            self.logger.error(f"下载图片失败 {image_url}: {e}")
            return None
    
    def close_spider(self, spider):
        """爬虫关闭时的清理工作"""
        self.logger.info(f"图片下载管道关闭，图片保存目录: {self.images_dir}")