import pymysql
import json
from datetime import datetime
from dyttspider.items import MovieLinkItem


class MovieLinkPipeline:
    """电影链接数据处理管道"""
    
    def __init__(self, mysql_host, mysql_port, mysql_user, mysql_password, mysql_db):
        self.mysql_host = mysql_host
        self.mysql_port = mysql_port
        self.mysql_user = mysql_user
        self.mysql_password = mysql_password
        self.mysql_db = mysql_db
        self.connection = None
        self.cursor = None

    @classmethod
    def from_crawler(cls, crawler):
        """从Scrapy设置中获取数据库配置"""
        db_settings = crawler.settings.getdict("DATABASE") or {}
        return cls(
            mysql_host=db_settings.get('host', '127.0.0.1'),
            mysql_port=db_settings.get('port', 3306),
            mysql_user=db_settings.get('user', 'root'),
            mysql_password=db_settings.get('password', 'root'),
            mysql_db=db_settings.get('name', 'dytt2026')
        )
    
    def open_spider(self, spider):
        """爬虫开始时初始化数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.mysql_host,
                port=self.mysql_port,
                user=self.mysql_user,
                password=self.mysql_password,
                database=self.mysql_db,
                charset='utf8mb4',
                autocommit=True
            )
            self.cursor = self.connection.cursor()
            
            # 创建电影链接表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS movie_links (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(500),
                url VARCHAR(500) UNIQUE,
                category VARCHAR(200),
                list_page_url VARCHAR(500),
                crawl_time VARCHAR(100),
                status VARCHAR(20) DEFAULT 'pending',
                retry_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_status (status),
                INDEX idx_category (category),
                INDEX idx_url (url)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """
            
            self.cursor.execute(create_table_sql)
            spider.logger.info("MySQL数据库连接成功，电影链接表已创建")
            
        except Exception as e:
            spider.logger.error(f"MySQL数据库连接失败: {e}")
            raise
    
    def close_spider(self, spider):
        """爬虫结束时关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        spider.logger.info("MySQL数据库连接已关闭")
    
    def process_item(self, item, spider):
        """处理电影链接项目"""
        if isinstance(item, MovieLinkItem):
            try:
                # 使用INSERT ... ON DUPLICATE KEY UPDATE语法
                insert_sql = """
                INSERT INTO movie_links 
                (title, url, category, list_page_url, crawl_time, status, retry_count)
                VALUES (%(title)s, %(url)s, %(category)s, %(list_page_url)s, %(crawl_time)s, %(status)s, %(retry_count)s)
                ON DUPLICATE KEY UPDATE
                    title = VALUES(title),
                    category = VALUES(category),
                    list_page_url = VALUES(list_page_url),
                    crawl_time = VALUES(crawl_time),
                    updated_at = CURRENT_TIMESTAMP
                """
                
                link_data = {
                    'title': item['title'],
                    'url': item['url'],
                    'category': item['category'],
                    'list_page_url': item['list_page_url'],
                    'crawl_time': item['crawl_time'],
                    'status': item['status'],
                    'retry_count': item['retry_count']
                }
                
                self.cursor.execute(insert_sql, link_data)
                spider.logger.info(f"电影链接已保存到MySQL: {item['title']}")
                
            except Exception as e:
                spider.logger.error(f"保存电影链接到MySQL失败: {e}")
                spider.logger.error(f"链接数据: {item}")
                raise
        
        return item
    
    def get_pending_links(self, limit=10):
        """获取待爬取的电影链接"""
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT id, title, url, category, retry_count
            FROM movie_links 
            WHERE status = 'pending' AND retry_count < 3
            ORDER BY created_at ASC
            LIMIT %s
        """, (limit,))
        return cursor.fetchall()
    
    def update_link_status(self, link_id, status, retry_count=None):
        """更新链接状态"""
        if retry_count is not None:
            self.cursor.execute("""
                UPDATE movie_links 
                SET status = %s, retry_count = %s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (status, retry_count, link_id))
        else:
            self.cursor.execute("""
                UPDATE movie_links 
                SET status = %s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (status, link_id))
        self.connection.commit()