import scrapy
import pymysql
import re
from urllib.parse import urljoin
from datetime import datetime
from dyttspider.items import MovieItem
from dyttspider.pipelines_link import MovieLinkPipeline
import chardet


class DetailCrawlerSpider(scrapy.Spider):
    name = 'detail_crawler'
    allowed_domains = ['dytt8899.com']
    
    custom_settings = {
        'ITEM_PIPELINES': {
            'dyttspider.pipelines_image.MovieImagePipeline': 200,
            'dyttspider.pipelines.MoviePipeline': 300,
        }
    }
    
    def __init__(self, *args, **kwargs):
        super(DetailCrawlerSpider, self).__init__(*args, **kwargs)
        self.batch_size = int(kwargs.get('batch_size', 10))  # 每批处理的链接数量
        
        # MySQL数据库配置
        self.mysql_config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': 'root',
            'database': 'dytt2026',
            'charset': 'utf8mb4'
        }
    
    def start_requests(self):
        """从数据库中获取待爬取的电影链接"""
        try:
            connection = pymysql.connect(**self.mysql_config)
            cursor = connection.cursor()
            
            # 获取待爬取的链接
            cursor.execute("""
                SELECT id, title, url, category, retry_count
                FROM movie_links 
                WHERE status = 'pending' AND retry_count < 3
                ORDER BY created_at ASC
                LIMIT %s
            """, (self.batch_size,))
            
            pending_links = cursor.fetchall()
            cursor.close()
            connection.close()
            
            if not pending_links:
                self.logger.info("没有找到待爬取的电影链接")
                return
            
            self.logger.info(f"找到 {len(pending_links)} 个待爬取的电影链接")
            
            # 为每个链接创建请求
            for link_id, title, url, category, retry_count in pending_links:
                # 更新状态为爬取中
                self.update_link_status(link_id, 'crawling')
                
                yield scrapy.Request(
                    url=url,
                    callback=self.parse_movie,
                    meta={
                        'link_id': link_id,
                        'movie_title': title,
                        'movie_category': category,
                        'retry_count': retry_count
                    },
                    errback=self.handle_error
                )
                
        except Exception as e:
            self.logger.error(f"从数据库获取链接失败: {e}")
    
    def parse_movie(self, response):
        """解析单个电影详情页面"""
        link_id = response.meta['link_id']
        
        try:
            # 检测和处理编码问题
            if hasattr(response, 'body'):
                # 检测原始字节的编码
                detected = chardet.detect(response.body)
                detected_encoding = detected.get('encoding', 'utf-8')
                confidence = detected.get('confidence', 0)
                
                self.logger.info(f"详情页检测到编码: {detected_encoding}, 置信度: {confidence}")
                
                # 如果检测到的编码不是UTF-8且置信度较高，尝试重新解码
                if detected_encoding and detected_encoding.lower() not in ['utf-8', 'ascii'] and confidence > 0.7:
                    try:
                        # 使用检测到的编码重新解码
                        decoded_text = response.body.decode(detected_encoding)
                        self.logger.info(f"详情页使用 {detected_encoding} 编码成功解码")
                        # 重新创建response对象或使用解码后的文本
                        response_text = decoded_text
                    except (UnicodeDecodeError, LookupError) as e:
                        self.logger.warning(f"详情页使用 {detected_encoding} 解码失败: {e}，尝试其他编码")
                        # 尝试常见的中文编码
                        for encoding in ['gbk', 'gb2312', 'gb18030', 'big5']:
                            try:
                                response_text = response.body.decode(encoding)
                                self.logger.info(f"详情页使用 {encoding} 编码成功解码")
                                break
                            except (UnicodeDecodeError, LookupError):
                                continue
                        else:
                            # 如果所有编码都失败，使用错误处理方式
                            response_text = response.body.decode('utf-8', errors='ignore')
                            self.logger.warning("详情页所有编码尝试失败，使用UTF-8忽略错误模式")
                else:
                    response_text = response.text
            else:
                response_text = response.text
                
            item = MovieItem()
            
            # 电影标题 - 尝试多种选择器
            title = response.css('div.title_all h1 font::text').get()
            if not title:
                title = response.css('div.title_all h1::text').get()
            if not title:
                title = response.css('h1::text').get()
            if not title:
                title = response.css('title::text').get()
                if title:
                    title = title.replace('_电影天堂', '').replace('_迅雷电影下载', '')
            
            item['title'] = title.strip() if title else response.meta['movie_title']
            
            # 电影URL
            item['url'] = response.url
            
            # 只从 div.co_content8 元素中获取电影详情内容
            content_div = response.css('div.co_content8')
            if not content_div:
                self.logger.warning(f"未找到 co_content8 元素: {response.url}")
                self.update_link_status(link_id, 'failed', response.meta['retry_count'] + 1)
                return
            
            # 发布时间 - 设置为当前时间
            item['publish_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 只获取 co_content8 元素内的文本内容
            co_content8_text = ' '.join(content_div.css('*::text').getall())
            movie_text = ' '.join([text.strip() for text in co_content8_text.split() if text.strip()])
            
            # 如果从CSS选择器获取的文本为空或包含乱码，尝试使用解码后的文本
            if not movie_text or len([c for c in movie_text if ord(c) > 127 and not ('\u4e00' <= c <= '\u9fff')]) > len(movie_text) * 0.3:
                # 如果乱码字符超过30%，使用正则表达式从解码后的文本中提取
                from scrapy import Selector
                
                # 创建新的选择器使用解码后的文本
                selector = Selector(text=response_text)
                content_div_new = selector.css('div.co_content8')
                if content_div_new:
                    co_content8_text = ' '.join(content_div_new.css('*::text').getall())
                    movie_text = ' '.join([text.strip() for text in co_content8_text.split() if text.strip()])
                    self.logger.info("使用解码后的文本重新提取内容")
            
            # 提取译名
            translated_title_match = re.search(r'◎译[\s　]*名[\s　]*[：:]?[\s　]*(.*?)(?=◎|导演|主演|编剧|类型|地区|语言|上映|片长|又名|IMDb|豆瓣|年代|$)', movie_text)
            if not translated_title_match:
                translated_title_match = re.search(r'译名[：:](.*?)(?=导演|主演|编剧|类型|地区|语言|上映|片长|又名|IMDb|豆瓣|年代|$)', movie_text)
            item['translated_title'] = translated_title_match.group(1).strip() if translated_title_match else ''
            
            # 提取片名（原始标题）
            original_title_match = re.search(r'◎片[\s　]*名[\s　]*[：:]?[\s　]*(.*?)(?=◎|导演|主演|编剧|类型|地区|语言|上映|片长|又名|IMDb|豆瓣|年代|译名|$)', movie_text)
            if not original_title_match:
                original_title_match = re.search(r'片名[：:](.*?)(?=导演|主演|编剧|类型|地区|语言|上映|片长|又名|IMDb|豆瓣|年代|译名|$)', movie_text)
            item['original_title'] = original_title_match.group(1).strip() if original_title_match else ''
            
            # 提取电影年份
            year_match = re.search(r'◎年[\s　]*代[\s　]*([0-9]{4})', movie_text)
            if not year_match:
                year_match = re.search(r'([0-9]{4})年', movie_text)
            if not year_match:
                year_match = re.search(r'年代[：:]([0-9]{4})', movie_text)
            if not year_match:
                year_match = re.search(r'◎年[\s　]*[：:]?[\s　]*([0-9]{4})', movie_text)
            item['year'] = year_match.group(1) if year_match else ''
            
            # 提取导演
            director_match = re.search(r'◎导[\s　]*演[\s　]*[：:]?[\s　]*(.*?)(?=◎|主演|编剧|类型|地区|语言|上映|片长|又名|IMDb|豆瓣|$)', movie_text)
            if not director_match:
                director_match = re.search(r'导演[：:](.*?)(?=主演|编剧|类型|地区|语言|上映|片长|又名|IMDb|豆瓣|$)', movie_text)
            item['director'] = director_match.group(1).strip() if director_match else ''
            
            # 提取主演
            actors_match = re.search(r'◎主[\s　]*演[\s　]*[：:]?[\s　]*(.*?)(?=◎|导演|编剧|类型|地区|语言|上映|片长|又名|IMDb|豆瓣|简|$)', movie_text)
            if not actors_match:
                actors_match = re.search(r'主演[：:](.*?)(?=导演|编剧|类型|地区|语言|上映|片长|又名|IMDb|豆瓣|$)', movie_text)
            item['actors'] = actors_match.group(1).strip() if actors_match else ''
            
            # 提取类型
            category_match = re.search(r'◎类[\s　]*别[\s　]*[：:]?[\s　]*(.*?)(?=◎|导演|主演|编剧|地区|语言|上映|片长|又名|IMDb|豆瓣|$)', movie_text)
            if not category_match:
                category_match = re.search(r'类型[：:](.*?)(?=导演|主演|编剧|地区|语言|上映|片长|又名|IMDb|豆瓣|$)', movie_text)
            item['category'] = category_match.group(1).strip() if category_match else response.meta['movie_category']
            
            # 提取评分
            rating_match = re.search(r'◎评分[：:]?\s*(\d+\.\d+)', movie_text)
            if not rating_match:
                rating_match = re.search(r'豆瓣评分[：:]?\s*(\d+\.\d+)', movie_text)
            if not rating_match:
                rating_match = re.search(r'IMDb评分[：:]?\s*(\d+\.\d+)', movie_text)
            if not rating_match:
                rating_match = re.search(r'评分[：:]?\s*(\d+\.\d+)', movie_text)
            item['rating'] = rating_match.group(1) if rating_match else ''
            
            # 提取简介内容
            plot_summary_match = re.search(r'◎简[\s　]*介[：:]?[\s　\n]*(.*?)(?=◎|【下载地址】|magnet:|thunder:|ftp:|ed2k:|$)', movie_text, re.DOTALL)
            if not plot_summary_match:
                plot_summary_match = re.search(r'剧情简介[：:]?[\s　\n]*(.*?)(?=◎|【下载地址】|magnet:|thunder:|ftp:|ed2k:|$)', movie_text, re.DOTALL)
            if not plot_summary_match:
                plot_summary_match = re.search(r'简介[：:]?[\s　\n]*(.*?)(?=◎|【下载地址】|magnet:|thunder:|ftp:|ed2k:|$)', movie_text, re.DOTALL)
            
            plot_summary = ''
            if plot_summary_match:
                plot_summary = plot_summary_match.group(1).strip()
                plot_summary = re.sub(r'\s+', ' ', plot_summary)
                plot_summary = re.sub(r'<[^>]+>', '', plot_summary)
            
            item['plot_summary'] = plot_summary
            
            # 电影描述
            description_parts = []
            if item['year']:
                description_parts.append(f"年份: {item['year']}")
            if item['director']:
                description_parts.append(f"导演: {item['director']}")
            if item['actors']:
                description_parts.append(f"主演: {item['actors']}")
            if item['category']:
                description_parts.append(f"类型: {item['category']}")
            
            item['description'] = ' | '.join(description_parts) if description_parts else movie_text[:200]
            
            # 提取下载链接
            download_links = []
            magnet_links = content_div.css('a[href^="magnet:"]::attr(href)').getall()
            download_links.extend(magnet_links)
            
            thunder_links = content_div.css('a[href^="thunder:"]::attr(href)').getall()
            download_links.extend(thunder_links)
            
            ftp_links = content_div.css('a[href^="ftp:"]::attr(href)').getall()
            download_links.extend(ftp_links)
            
            ed2k_links = content_div.css('a[href^="ed2k:"]::attr(href)').getall()
            download_links.extend(ed2k_links)
            
            item['download_links'] = download_links
            
            # 提取电影海报图片
            poster_images = content_div.css('img::attr(src)').getall()
            poster_url = ''
            if poster_images:
                # 选择第一个图片作为海报，通常是电影海报
                for img_url in poster_images:
                    if img_url and ('jpg' in img_url.lower() or 'jpeg' in img_url.lower() or 'png' in img_url.lower()):
                        # 如果是相对路径，转换为绝对路径
                        if img_url.startswith('/'):
                            poster_url = urljoin(response.url, img_url)
                        elif img_url.startswith('http'):
                            poster_url = img_url
                        else:
                            poster_url = urljoin(response.url, img_url)
                        break
            
            item['poster_url'] = poster_url
            self.logger.info(f"提取到海报链接: {poster_url}")
            
            # 更新链接状态为完成
            self.update_link_status(link_id, 'completed')
            
            yield item
            
        except Exception as e:
            self.logger.error(f"解析电影详情失败 {response.url}: {e}")
            self.update_link_status(link_id, 'failed', response.meta['retry_count'] + 1)
    
    def handle_error(self, failure):
        """处理请求错误"""
        link_id = failure.request.meta['link_id']
        retry_count = failure.request.meta['retry_count']
        
        self.logger.error(f"请求失败 {failure.request.url}: {failure.value}")
        self.update_link_status(link_id, 'failed', retry_count + 1)
    
    def update_link_status(self, link_id, status, retry_count=None):
        """更新链接状态"""
        try:
            connection = pymysql.connect(**self.mysql_config)
            cursor = connection.cursor()
            
            if retry_count is not None:
                cursor.execute("""
                    UPDATE movie_links 
                    SET status = %s, retry_count = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                """, (status, retry_count, link_id))
            else:
                cursor.execute("""
                    UPDATE movie_links 
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                """, (status, link_id))
            
            connection.commit()
            cursor.close()
            connection.close()
            
        except Exception as e:
            self.logger.error(f"更新链接状态失败: {e}")