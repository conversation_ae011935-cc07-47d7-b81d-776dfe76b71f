import scrapy
import re
import pymysql
from urllib.parse import urljoin
from datetime import datetime
from dyttspider.items import MovieLinkItem
import chardet


class LinkCollectorSpider(scrapy.Spider):
    name = 'link_collector'
    allowed_domains = ['dytt8899.com']
    
    def __init__(self, *args, **kwargs):
        super(LinkCollectorSpider, self).__init__(*args, **kwargs)
        self.failed_pages = []  # 记录失败的页面
        self.retry_count = 0  # 重试次数
        self.max_retries = 3  # 最大重试次数
        
        # 数据库配置
        self.db_config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': 'root',
            'database': 'dytt2026',
            'charset': 'utf8mb4'
        }
    
    # 不同分类的起始URL
    start_urls = [
        'https://www.dytt8899.com/html/gndy/dyzz/',  # 最新电影
        # 'https://www.dytt8899.com/html/gndy/china/',  # 华语电影
        # 'https://www.dytt8899.com/html/gndy/oumei/',  # 欧美电影
        # 'https://www.dytt8899.com/html/gndy/rihan/',  # 日韩电影
    ]
    
    # 分类映射
    category_mapping = {
        'dyzz': '最新电影',
        'china': '华语电影', 
        'oumei': '欧美电影',
        'rihan': '日韩电影'
    }
    
    custom_settings = {
        'ITEM_PIPELINES': {
            'dyttspider.pipelines.MovieLinkPipeline': 300,
        }
    }
    
    def parse(self, response):
        """解析电影列表页面，提取电影详情页链接"""
        try:
            # 检测和处理编码问题
            if hasattr(response, 'body'):
                # 检测原始字节的编码
                detected = chardet.detect(response.body)
                detected_encoding = detected.get('encoding', 'utf-8')
                confidence = detected.get('confidence', 0)
                
                self.logger.info(f"列表页检测到编码: {detected_encoding}, 置信度: {confidence}")
                
                # 如果检测到的编码不是UTF-8且置信度较高，尝试重新解码
                if detected_encoding and detected_encoding.lower() not in ['utf-8', 'ascii'] and confidence > 0.7:
                    try:
                        # 使用检测到的编码重新解码
                        decoded_text = response.body.decode(detected_encoding)
                        self.logger.info(f"列表页使用 {detected_encoding} 编码成功解码")
                        # 重新创建response对象或使用解码后的文本
                        response_text = decoded_text
                    except (UnicodeDecodeError, LookupError) as e:
                        self.logger.warning(f"列表页使用 {detected_encoding} 解码失败: {e}，尝试其他编码")
                        # 尝试常见的中文编码
                        for encoding in ['gbk', 'gb2312', 'gb18030', 'big5']:
                            try:
                                response_text = response.body.decode(encoding)
                                self.logger.info(f"列表页使用 {encoding} 编码成功解码")
                                break
                            except (UnicodeDecodeError, LookupError):
                                continue
                        else:
                            # 如果所有编码都失败，使用错误处理方式
                            response_text = response.body.decode('utf-8', errors='ignore')
                            self.logger.warning("列表页所有编码尝试失败，使用UTF-8忽略错误模式")
                else:
                    response_text = response.text
            else:
                response_text = response.text
                
            # 从URL中提取分类
            category_key = self.extract_category_from_url(response.url)
            category = self.category_mapping.get(category_key, '未知分类')
            
            # 从 co_content8 元素中获取电影链接
            content_div = response.css('div.co_content8')
            if content_div:
                # 获取co_content8下面table中的所有电影链接，包括详情页(/i/)和列表页(/html/gndy/)
                movie_links = content_div.css('table a[href$=".html"]')
            else:
                # 如果没有找到 co_content8，使用全局搜索作为备选
                movie_links = response.css('table a[href$=".html"]')
            
            # 检查是否成功获取到电影链接
            if not movie_links:
                # 如果没有找到链接，尝试使用解码后的文本重新提取
                from scrapy import Selector
                
                # 创建新的选择器使用解码后的文本
                selector = Selector(text=response_text)
                content_div_new = selector.css('div.co_content8')
                if content_div_new:
                    movie_links = content_div_new.css('table a[href$=".html"]')
                    if movie_links:
                        self.logger.info(f"使用解码后的文本重新提取到 {len(movie_links)} 个电影链接")
                    else:
                        movie_links = selector.css('table a[href$=".html"]')
                        if movie_links:
                            self.logger.info(f"使用解码后的文本全局搜索到 {len(movie_links)} 个电影链接")
                
                if not movie_links:
                    self.logger.warning(f"页面 {response.url} 未找到电影链接，可能需要重试")
                    failed_page_info = {
                        'url': response.url,
                        'reason': 'no_movie_links',
                        'status_code': response.status,
                        'retry_count': response.meta.get('retry_count', 0)
                    }
                    self.failed_pages.append(failed_page_info)
                    self.save_failed_page_to_db(failed_page_info, category)
                    # 不要return，继续处理翻页逻辑
            else:
                self.logger.info(f"解析页面 {response.url}，找到 {len(movie_links)} 个电影链接")
                
                # 标记页面为已解决（如果之前失败过）
                self.mark_page_as_resolved(response.url)
                
                # 统计变量
                new_links_count = 0
                skipped_links_count = 0
                
                for link in movie_links:
                    item = MovieLinkItem()
                    
                    # 电影标题
                    title = link.css('::text').get()
                    if title:
                        title = title.strip()
                    
                    # 电影详情页URL
                    relative_url = link.css('::attr(href)').get()
                    if relative_url:
                        movie_url = urljoin(response.url, relative_url)
                        
                        # 检查数据库中是否已存在此电影链接
                        exists_in_db = self.check_movie_link_exists(movie_url, title)
                        
                        if exists_in_db:
                            # 只在调试模式下输出跳过的日志，避免大量重复输出
                            self.logger.debug(f"MySQL检查: 电影链接已存在于数据库中，跳过 - {title} ({movie_url})")
                            skipped_links_count += 1
                            continue
                        
                        # 只在调试模式下记录每个新电影链接的详细信息
                        self.logger.debug(f"发现新电影链接: {title} ({movie_url})")
                        new_links_count += 1
                        
                        item['title'] = title or ''
                        item['url'] = movie_url
                        item['category'] = category
                        item['list_page_url'] = response.url
                        item['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        item['status'] = 'pending'
                        item['retry_count'] = 0
                        
                        yield item
                
                # 输出页面处理统计信息
                if new_links_count > 0 or skipped_links_count > 0:
                    self.logger.info(f"页面处理完成 {response.url}: 新增 {new_links_count} 个链接，跳过 {skipped_links_count} 个已存在链接")

            
            # 处理翻页
            next_page = response.css('a[title="下一页"]::attr(href)').get()
            if not next_page:
                next_page = response.css('a:contains("下一页")::attr(href)').get()
            if not next_page:
                next_page = response.css('a:contains("下页")::attr(href)').get()
                
            if next_page:
                next_page_url = urljoin(response.url, next_page)
                self.logger.info(f"发现下一页链接: {next_page_url}")
                yield scrapy.Request(
                    url=next_page_url, 
                    callback=self.parse,
                    errback=self.handle_error,
                    meta={'retry_count': 0}
                )
            else:
                self.logger.info(f"页面 {response.url} 没有找到下一页链接，可能已到最后一页")
                
        except Exception as e:
            self.logger.error(f"解析页面 {response.url} 时发生错误: {e}")
            category_key = self.extract_category_from_url(response.url)
            category = self.category_mapping.get(category_key, '未知分类')
            failed_page_info = {
                'url': response.url,
                'reason': f'parse_error: {str(e)}',
                'status_code': response.status,
                'retry_count': response.meta.get('retry_count', 0)
            }
            self.failed_pages.append(failed_page_info)
            self.save_failed_page_to_db(failed_page_info, category)
    
    def handle_error(self, failure):
        """处理请求失败的情况"""
        request = failure.request
        retry_count = request.meta.get('retry_count', 0)
        
        self.logger.error(f"请求失败: {request.url}, 错误: {failure.value}, 重试次数: {retry_count}")
        
        # 记录失败的页面
        category_key = self.extract_category_from_url(request.url)
        category = self.category_mapping.get(category_key, '未知分类')
        failed_page_info = {
            'url': request.url,
            'reason': f'request_failed: {str(failure.value)}',
            'status_code': getattr(failure.value, 'response', {}).get('status', 'unknown') if hasattr(failure.value, 'response') else 'unknown',
            'retry_count': retry_count
        }
        self.failed_pages.append(failed_page_info)
        self.save_failed_page_to_db(failed_page_info, category)
    
    def start_requests(self):
        """生成初始请求，包括从数据库加载的失败页面"""
        # 首先处理正常的起始URL
        for url in self.start_urls:
            yield scrapy.Request(
                url=url,
                callback=self.parse,
                errback=self.handle_error,
                meta={'retry_count': 0}
            )
        
        # 然后处理数据库中的失败页面
        db_failed_pages = self.get_failed_pages_from_db()
        if db_failed_pages:
            self.logger.info(f"从数据库加载了 {len(db_failed_pages)} 个失败页面进行重试")
            for failed_page in db_failed_pages:
                yield scrapy.Request(
                    url=failed_page['url'],
                    callback=self.parse,
                    errback=self.handle_error,
                    meta={'retry_count': failed_page['retry_count']},
                    dont_filter=True
                )
    
    def closed(self, reason):
        """爬虫关闭时的处理"""
        self.logger.info(f"爬虫关闭，原因: {reason}")
        
        if self.failed_pages:
            self.logger.warning(f"本次运行发现 {len(self.failed_pages)} 个失败的页面，已保存到数据库")
            
            # 统计失败原因
            failure_reasons = {}
            for failed_page in self.failed_pages:
                reason = failed_page['reason']
                failure_reasons[reason] = failure_reasons.get(reason, 0) + 1
            
            self.logger.info("失败原因统计:")
            for reason, count in failure_reasons.items():
                self.logger.info(f"  - {reason}: {count} 个页面")
        else:
            self.logger.info("本次运行所有页面都成功爬取完成！")
        
        # 显示数据库中的失败页面统计
        try:
            connection = pymysql.connect(**self.db_config)
            cursor = connection.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) as total, 
                       SUM(CASE WHEN is_resolved = FALSE THEN 1 ELSE 0 END) as unresolved
                FROM failed_list_pages
            """)
            
            result = cursor.fetchone()
            if result:
                total, unresolved = result
                self.logger.info(f"数据库中失败页面统计: 总计 {total} 个，未解决 {unresolved} 个")
            
        except Exception as e:
            self.logger.error(f"查询数据库失败页面统计时出错: {e}")
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'connection' in locals():
                connection.close()
    
    def extract_category_from_url(self, url):
        """从URL中提取分类标识"""
        if '/dyzz/' in url:
            return 'dyzz'
        elif '/china/' in url:
            return 'china'
        elif '/oumei/' in url:
            return 'oumei'
        elif '/rihan/' in url:
            return 'rihan'
        else:
            return 'unknown'
    
    def save_failed_page_to_db(self, failed_page_info, category):
        """保存失败页面信息到数据库"""
        try:
            connection = pymysql.connect(**self.db_config)
            cursor = connection.cursor()
            
            # 使用INSERT ... ON DUPLICATE KEY UPDATE避免重复插入
            insert_sql = """
            INSERT INTO failed_list_pages 
            (url, category, failure_reason, status_code, retry_count, last_attempt_time)
            VALUES (%(url)s, %(category)s, %(failure_reason)s, %(status_code)s, %(retry_count)s, NOW())
            ON DUPLICATE KEY UPDATE
                failure_reason = VALUES(failure_reason),
                status_code = VALUES(status_code),
                retry_count = retry_count + 1,
                last_attempt_time = NOW(),
                updated_at = NOW()
            """
            
            data = {
                'url': failed_page_info['url'],
                'category': category,
                'failure_reason': failed_page_info['reason'],
                'status_code': str(failed_page_info['status_code']),
                'retry_count': failed_page_info['retry_count']
            }
            
            cursor.execute(insert_sql, data)
            connection.commit()
            
            self.logger.info(f"失败页面已保存到数据库: {failed_page_info['url']}")
            
        except Exception as e:
            self.logger.error(f"保存失败页面到数据库时出错: {e}")
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'connection' in locals():
                connection.close()
    
    def get_failed_pages_from_db(self, max_retry_count=3):
        """从数据库获取未解决的失败页面"""
        failed_pages = []
        try:
            connection = pymysql.connect(**self.db_config)
            cursor = connection.cursor()
            
            # 获取未解决且重试次数未超限的失败页面
            select_sql = """
            SELECT url, category, failure_reason, status_code, retry_count
            FROM failed_list_pages 
            WHERE is_resolved = FALSE AND retry_count < %s
            ORDER BY last_attempt_time ASC
            """
            
            cursor.execute(select_sql, (max_retry_count,))
            results = cursor.fetchall()
            
            for result in results:
                url, category, failure_reason, status_code, retry_count = result
                failed_pages.append({
                    'url': url,
                    'category': category,
                    'reason': failure_reason,
                    'status_code': status_code,
                    'retry_count': retry_count
                })
            
            self.logger.info(f"从数据库获取到 {len(failed_pages)} 个待重试的失败页面")
            
        except Exception as e:
            self.logger.error(f"从数据库获取失败页面时出错: {e}")
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'connection' in locals():
                connection.close()
        
        return failed_pages
    
    def mark_page_as_resolved(self, url):
        """标记页面为已解决"""
        try:
            connection = pymysql.connect(**self.db_config)
            cursor = connection.cursor()
            
            update_sql = """
            UPDATE failed_list_pages 
            SET is_resolved = TRUE, resolved_at = NOW(), updated_at = NOW()
            WHERE url = %s
            """
            
            cursor.execute(update_sql, (url,))
            connection.commit()
            
            if cursor.rowcount > 0:
                self.logger.info(f"页面已标记为已解决: {url}")
            
        except Exception as e:
            self.logger.error(f"标记页面为已解决时出错: {e}")
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'connection' in locals():
                connection.close()
    
    def check_movie_link_exists(self, movie_url, title):
        """检查电影链接是否已存在于数据库中"""
        try:
            connection = pymysql.connect(**self.db_config)
            cursor = connection.cursor()
            
            # 检查movie_links表中是否已存在该URL
            check_sql = """
            SELECT COUNT(*) FROM movie_links 
            WHERE url = %s
            """
            
            cursor.execute(check_sql, (movie_url,))
            result = cursor.fetchone()
            
            exists = result[0] > 0 if result else False
            
            return exists
            
        except Exception as e:
            self.logger.error(f"检查电影链接是否存在时出错: {e}")
            # 如果查询出错，返回False以确保不会跳过爬取
            return False
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'connection' in locals():
                connection.close()