#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Logging utility module for DYTT Spider
"""

import logging
import logging.handlers
import os
from datetime import datetime


class SpiderLogger:
    """Custom logger for spider operations"""
    
    def __init__(self, name='dyttspider', log_dir='logs'):
        self.name = name
        self.log_dir = log_dir
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger with file and console handlers"""
        # Create logs directory if it doesn't exist
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        
        # Create logger
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.DEBUG)
        
        # Avoid duplicate handlers
        if self.logger.handlers:
            return
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s [%(name)s] %(levelname)s [%(filename)s:%(lineno)d] %(funcName)s(): %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        simple_formatter = logging.Formatter(
            '%(asctime)s [%(name)s] %(levelname)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        
        # File handler for all logs
        file_handler = logging.handlers.RotatingFileHandler(
            os.path.join(self.log_dir, f'{self.name}.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        
        # Error file handler
        error_handler = logging.handlers.RotatingFileHandler(
            os.path.join(self.log_dir, f'{self.name}_error.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        
        # Add handlers to logger
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
    
    def get_logger(self):
        """Get the configured logger"""
        return self.logger
    
    def log_spider_start(self, spider_name, settings=None):
        """Log spider start information"""
        self.logger.info(f"Starting spider: {spider_name}")
        if settings:
            self.logger.info(f"Spider settings: {settings}")
    
    def log_spider_end(self, spider_name, stats=None):
        """Log spider end information"""
        self.logger.info(f"Spider finished: {spider_name}")
        if stats:
            self.logger.info(f"Spider stats: {stats}")
    
    def log_item_processed(self, item_type, item_data=None):
        """Log item processing"""
        self.logger.debug(f"Processed {item_type} item")
        if item_data:
            self.logger.debug(f"Item data: {item_data}")
    
    def log_database_operation(self, operation, table, count=None):
        """Log database operations"""
        msg = f"Database {operation} on table '{table}'"
        if count is not None:
            msg += f" - {count} records"
        self.logger.info(msg)
    
    def log_error(self, error, context=None):
        """Log error with context"""
        msg = f"Error occurred: {error}"
        if context:
            msg += f" - Context: {context}"
        self.logger.error(msg, exc_info=True)
    
    def log_request_info(self, url, status_code=None, response_time=None):
        """Log request information"""
        msg = f"Request to {url}"
        if status_code:
            msg += f" - Status: {status_code}"
        if response_time:
            msg += f" - Time: {response_time:.2f}s"
        self.logger.debug(msg)


def get_spider_logger(name='dyttspider', log_dir='logs'):
    """Get a configured spider logger"""
    logger_instance = SpiderLogger(name, log_dir)
    return logger_instance.get_logger()


def setup_logging(log_dir='logs', log_level='INFO'):
    """Setup basic logging configuration"""
    # Create logs directory
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s [%(name)s] %(levelname)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.StreamHandler(),
            logging.handlers.RotatingFileHandler(
                os.path.join(log_dir, 'spider.log'),
                maxBytes=10*1024*1024,
                backupCount=5,
                encoding='utf-8'
            )
        ]
    )