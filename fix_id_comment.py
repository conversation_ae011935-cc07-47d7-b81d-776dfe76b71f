#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 id 字段备注
"""

import pymysql

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'dytt2026',
    'charset': 'utf8mb4'
}

def fix_id_comment():
    """修复 movie_links 表 id 字段的备注"""
    
    try:
        print("🔄 连接数据库...")
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("✅ 数据库连接成功")
        print("\n🔄 修复 movie_links 表 id 字段备注...")
        
        # 只修改 id 字段的备注，不重新定义主键
        statement = "ALTER TABLE movie_links MODIFY COLUMN id INT AUTO_INCREMENT COMMENT '主键ID，用于唯一标识每个电影链接'"
        
        try:
            cursor.execute(statement)
            print("  ✅ id 字段备注更新成功")
        except Exception as e:
            print(f"  ❌ id 字段备注更新失败: {e}")
        
        connection.commit()
        
        # 验证更新结果
        print("\n📋 验证更新结果:")
        cursor.execute("SHOW CREATE TABLE movie_links")
        result = cursor.fetchone()
        if result:
            create_sql = result[1]
            lines = create_sql.split('\n')
            for line in lines:
                if '`id`' in line:
                    print(f"  {line.strip()}")
                    break
        
        cursor.close()
        connection.close()
        
        print("\n🎉 修复完成！")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    fix_id_comment()
