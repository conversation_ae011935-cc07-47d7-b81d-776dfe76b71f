#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细数据库初始化脚本
创建规范化的电影数据库表结构
"""

import pymysql
import sys

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'charset': 'utf8mb4'
}

DATABASE_NAME = 'dytt2026'

def create_detailed_database():
    """创建详细的规范化数据库结构"""
    try:
        # 连接MySQL服务器
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 创建数据库
        create_db_sql = f"CREATE DATABASE IF NOT EXISTS {DATABASE_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        cursor.execute(create_db_sql)
        print(f"✅ 数据库 '{DATABASE_NAME}' 创建成功")
        
        # 选择数据库
        cursor.execute(f"USE {DATABASE_NAME}")
        
        # 1. 创建电影基本信息表
        movies_table_sql = """
        CREATE TABLE IF NOT EXISTS movies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(500) NOT NULL COMMENT '电影标题',
            original_title VARCHAR(500) COMMENT '原始标题',
            url VARCHAR(500) UNIQUE NOT NULL COMMENT '电影链接',
            year INT COMMENT '电影年份',
            duration INT COMMENT '电影时长(分钟)',
            rating DECIMAL(3,1) COMMENT '电影评分',
            rating_count INT COMMENT '评分人数',
            language VARCHAR(100) COMMENT '电影语言',
            country VARCHAR(200) COMMENT '制片国家/地区',
            release_date DATE COMMENT '上映日期',
            publish_time DATETIME COMMENT '发布时间',
            description TEXT COMMENT '电影描述',
            plot_summary TEXT COMMENT '剧情简介',
            poster_url VARCHAR(500) COMMENT '海报链接',
            poster_local_path VARCHAR(500) COMMENT '海报本地路径',
            imdb_id VARCHAR(20) COMMENT 'IMDB ID',
            douban_id VARCHAR(20) COMMENT '豆瓣ID',
            status ENUM('active', 'inactive', 'deleted') DEFAULT 'active' COMMENT '状态',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_title (title(100)),
            INDEX idx_year (year),
            INDEX idx_rating (rating),
            INDEX idx_release_date (release_date),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电影基本信息表';
        """
        
        # 2. 创建电影类型表
        genres_table_sql = """
        CREATE TABLE IF NOT EXISTS genres (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) UNIQUE NOT NULL COMMENT '类型名称',
            name_en VARCHAR(50) COMMENT '英文名称',
            description TEXT COMMENT '类型描述',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电影类型表';
        """
        
        # 3. 创建电影-类型关联表
        movie_genres_table_sql = """
        CREATE TABLE IF NOT EXISTS movie_genres (
            id INT AUTO_INCREMENT PRIMARY KEY,
            movie_id INT NOT NULL,
            genre_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
            FOREIGN KEY (genre_id) REFERENCES genres(id) ON DELETE CASCADE,
            UNIQUE KEY unique_movie_genre (movie_id, genre_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电影类型关联表';
        """
        
        # 4. 创建人员表（导演、演员等）
        persons_table_sql = """
        CREATE TABLE IF NOT EXISTS persons (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL COMMENT '姓名',
            name_en VARCHAR(200) COMMENT '英文名',
            gender ENUM('male', 'female', 'unknown') DEFAULT 'unknown' COMMENT '性别',
            birth_date DATE COMMENT '出生日期',
            birth_place VARCHAR(200) COMMENT '出生地',
            nationality VARCHAR(100) COMMENT '国籍',
            biography TEXT COMMENT '个人简介',
            photo_url VARCHAR(500) COMMENT '照片链接',
            imdb_id VARCHAR(20) COMMENT 'IMDB ID',
            douban_id VARCHAR(20) COMMENT '豆瓣ID',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_name (name),
            INDEX idx_birth_date (birth_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人员信息表';
        """
        
        # 5. 创建电影-人员关联表
        movie_persons_table_sql = """
        CREATE TABLE IF NOT EXISTS movie_persons (
            id INT AUTO_INCREMENT PRIMARY KEY,
            movie_id INT NOT NULL,
            person_id INT NOT NULL,
            role_type ENUM('director', 'actor', 'writer', 'producer', 'composer', 'cinematographer', 'editor') NOT NULL COMMENT '角色类型',
            character_name VARCHAR(200) COMMENT '角色名称（演员）',
            role_order INT DEFAULT 0 COMMENT '角色顺序',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
            FOREIGN KEY (person_id) REFERENCES persons(id) ON DELETE CASCADE,
            INDEX idx_movie_role (movie_id, role_type),
            INDEX idx_person_role (person_id, role_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电影人员关联表';
        """
        
        # 6. 创建下载链接表
        download_links_table_sql = """
        CREATE TABLE IF NOT EXISTS download_links (
            id INT AUTO_INCREMENT PRIMARY KEY,
            movie_id INT NOT NULL,
            link_type ENUM('magnet', 'torrent', 'ed2k', 'http', 'ftp') NOT NULL COMMENT '链接类型',
            url TEXT NOT NULL COMMENT '下载链接',
            file_name VARCHAR(500) COMMENT '文件名',
            file_size VARCHAR(50) COMMENT '文件大小',
            quality VARCHAR(50) COMMENT '画质（HD/BD/4K等）',
            format VARCHAR(20) COMMENT '格式（MP4/MKV/AVI等）',
            language VARCHAR(100) COMMENT '语言版本',
            subtitle VARCHAR(100) COMMENT '字幕信息',
            seeds INT DEFAULT 0 COMMENT '种子数',
            peers INT DEFAULT 0 COMMENT '下载者数',
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
            INDEX idx_movie_id (movie_id),
            INDEX idx_link_type (link_type),
            INDEX idx_quality (quality),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='下载链接表';
        """
        
        # 执行所有表创建语句
        tables = [
            ('movies', movies_table_sql),
            ('genres', genres_table_sql),
            ('movie_genres', movie_genres_table_sql),
            ('persons', persons_table_sql),
            ('movie_persons', movie_persons_table_sql),
            ('download_links', download_links_table_sql)
        ]
        
        for table_name, sql in tables:
            cursor.execute(sql)
            print(f"✅ 表 '{table_name}' 创建成功")
        
        # 插入一些基础数据
        insert_basic_data(cursor)
        
        cursor.close()
        connection.close()
        
        print("\n🎉 详细数据库结构创建完成！")
        print_database_info()
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        sys.exit(1)

def insert_basic_data(cursor):
    """插入基础数据"""
    # 插入常见电影类型
    genres_data = [
        ('动作', 'Action'),
        ('喜剧', 'Comedy'),
        ('剧情', 'Drama'),
        ('恐怖', 'Horror'),
        ('科幻', 'Science Fiction'),
        ('悬疑', 'Mystery'),
        ('爱情', 'Romance'),
        ('战争', 'War'),
        ('犯罪', 'Crime'),
        ('冒险', 'Adventure'),
        ('动画', 'Animation'),
        ('纪录片', 'Documentary'),
        ('音乐', 'Musical'),
        ('传记', 'Biography'),
        ('历史', 'History'),
        ('西部', 'Western'),
        ('奇幻', 'Fantasy'),
        ('惊悚', 'Thriller')
    ]
    
    for name, name_en in genres_data:
        try:
            cursor.execute(
                "INSERT IGNORE INTO genres (name, name_en) VALUES (%s, %s)",
                (name, name_en)
            )
        except:
            pass
    
    print("✅ 基础数据插入完成")

def print_database_info():
    """打印数据库信息"""
    print(f"\n📊 数据库信息:")
    print(f"   主机: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"   数据库: {DATABASE_NAME}")
    print(f"   用户: {DB_CONFIG['user']}")
    print(f"   字符集: {DB_CONFIG['charset']}")
    
    print(f"\n📋 数据表结构:")
    tables_info = [
        ('movies', '电影基本信息表'),
        ('genres', '电影类型表'),
        ('movie_genres', '电影-类型关联表'),
        ('persons', '人员信息表'),
        ('movie_persons', '电影-人员关联表'),
        ('download_links', '下载链接表')
    ]
    
    for table_name, description in tables_info:
        print(f"   {table_name:<20} - {description}")

if __name__ == "__main__":
    print("🚀 开始创建详细的电影数据库结构...")
    print("=" * 60)
    
    # 创建详细数据库结构
    create_detailed_database()
    
    print("\n🎯 详细数据库结构创建完成！")
    print("\n💡 提示: 现在需要更新爬虫代码以适配新的数据库结构")