[loggers]
keys=root,scrapy,dytt_spider,link_collector,detail_crawler

[handlers]
keys=console<PERSON><PERSON><PERSON>,file<PERSON>and<PERSON>,error<PERSON><PERSON><PERSON>,dytt<PERSON><PERSON><PERSON><PERSON><PERSON>,linkCollectorFile<PERSON><PERSON><PERSON>,detail<PERSON>rawler<PERSON><PERSON><PERSON><PERSON><PERSON>,dytt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,link<PERSON>ollector<PERSON><PERSON>r<PERSON><PERSON><PERSON>,detailCrawlerErrorHandler

[formatters]
keys=simpleFormatter,detailedFormatter

[logger_root]
level=INFO
handlers=consoleHandler,fileHandler

[logger_scrapy]
level=INFO
handlers=consoleHandler,fileHandler
qualname=scrapy
propagate=0

[logger_dytt_spider]
level=DEBUG
handlers=console<PERSON>andler,dytt<PERSON>ile<PERSON>andler,dyttErrorHandler
qualname=dytt_spider
propagate=0

[logger_link_collector]
level=DEBUG
handlers=consoleHandler,linkCollectorFileHandler,linkCollectorErrorHandler
qualname=link_collector
propagate=0

[logger_detail_crawler]
level=DEBUG
handlers=consoleHandler,detailCrawlerFile<PERSON><PERSON><PERSON>,detailCrawlerErrorHandler
qualname=detail_crawler
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=handlers.RotatingFileHandler
level=DEBUG
formatter=detailedFormatter
args=('logs/spider.log', 'a', 10485760, 5, 'utf-8')

[handler_errorHandler]
class=handlers.RotatingFileHandler
level=ERROR
formatter=detailedFormatter
args=('logs/error.log', 'a', 10485760, 3, 'utf-8')

[handler_dyttFileHandler]
class=handlers.RotatingFileHandler
level=DEBUG
formatter=detailedFormatter
args=('logs/dytt_spider.log', 'a', 10485760, 5, 'utf-8')

[handler_linkCollectorFileHandler]
class=handlers.RotatingFileHandler
level=DEBUG
formatter=detailedFormatter
args=('logs/link_collector.log', 'a', 10485760, 5, 'utf-8')

[handler_detailCrawlerFileHandler]
class=handlers.RotatingFileHandler
level=DEBUG
formatter=detailedFormatter
args=('logs/detail_crawler.log', 'a', 10485760, 5, 'utf-8')

[handler_dyttErrorHandler]
class=handlers.RotatingFileHandler
level=ERROR
formatter=detailedFormatter
args=('logs/dytt_spider_error.log', 'a', 10485760, 3, 'utf-8')

[handler_linkCollectorErrorHandler]
class=handlers.RotatingFileHandler
level=ERROR
formatter=detailedFormatter
args=('logs/link_collector_error.log', 'a', 10485760, 3, 'utf-8')

[handler_detailCrawlerErrorHandler]
class=handlers.RotatingFileHandler
level=ERROR
formatter=detailedFormatter
args=('logs/detail_crawler_error.log', 'a', 10485760, 3, 'utf-8')

[formatter_simpleFormatter]
format=%(asctime)s [%(name)s] %(levelname)s: %(message)s
datefmt=%Y-%m-%d %H:%M:%S

[formatter_detailedFormatter]
format=%(asctime)s [%(name)s] %(levelname)s [%(filename)s:%(lineno)d] %(funcName)s(): %(message)s
datefmt=%Y-%m-%d %H:%M:%S