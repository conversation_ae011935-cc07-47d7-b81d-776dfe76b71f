#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失败列表页管理工具
用于查看、管理和统计失败的列表页
"""

import pymysql
import argparse
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'dytt2026',
    'charset': 'utf8mb4'
}

class FailedPagesManager:
    """失败页面管理器"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
    
    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.db_config)
    
    def show_stats(self):
        """显示失败页面统计信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 总体统计
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN is_resolved = TRUE THEN 1 ELSE 0 END) as resolved,
                    SUM(CASE WHEN is_resolved = FALSE THEN 1 ELSE 0 END) as unresolved,
                    AVG(retry_count) as avg_retry
                FROM failed_list_pages
            """)
            
            total_stats = cursor.fetchone()
            
            print("📊 失败页面总体统计:")
            print(f"   总计: {total_stats[0] or 0} 个")
            print(f"   已解决: {total_stats[1] or 0} 个")
            print(f"   未解决: {total_stats[2] or 0} 个")
            print(f"   平均重试次数: {total_stats[3] or 0:.1f}")
            
            # 分类统计
            cursor.execute("""
                SELECT 
                    category,
                    COUNT(*) as total,
                    SUM(CASE WHEN is_resolved = TRUE THEN 1 ELSE 0 END) as resolved,
                    SUM(CASE WHEN is_resolved = FALSE THEN 1 ELSE 0 END) as unresolved
                FROM failed_list_pages 
                GROUP BY category
                ORDER BY total DESC
            """)
            
            category_stats = cursor.fetchall()
            
            if category_stats:
                print("\n📈 分类统计:")
                print(f"{'分类':<15} {'总计':<8} {'已解决':<8} {'未解决':<8}")
                print("-" * 45)
                for row in category_stats:
                    print(f"{row[0]:<15} {row[1]:<8} {row[2]:<8} {row[3]:<8}")
            
            # 失败原因统计
            cursor.execute("""
                SELECT 
                    failure_reason,
                    COUNT(*) as count,
                    SUM(CASE WHEN is_resolved = FALSE THEN 1 ELSE 0 END) as unresolved_count
                FROM failed_list_pages 
                GROUP BY failure_reason
                ORDER BY count DESC
            """)
            
            reason_stats = cursor.fetchall()
            
            if reason_stats:
                print("\n🔍 失败原因统计:")
                print(f"{'失败原因':<30} {'总计':<8} {'未解决':<8}")
                print("-" * 50)
                for row in reason_stats:
                    reason = row[0][:28] + ".." if len(row[0]) > 30 else row[0]
                    print(f"{reason:<30} {row[1]:<8} {row[2]:<8}")
            
            # 重试次数分布
            cursor.execute("""
                SELECT 
                    retry_count,
                    COUNT(*) as count
                FROM failed_list_pages 
                GROUP BY retry_count
                ORDER BY retry_count
            """)
            
            retry_stats = cursor.fetchall()
            
            if retry_stats:
                print("\n🔄 重试次数分布:")
                print(f"{'重试次数':<10} {'页面数量':<10}")
                print("-" * 25)
                for row in retry_stats:
                    print(f"{row[0]:<10} {row[1]:<10}")
    
    def list_failed_pages(self, limit=20, unresolved_only=False, category=None):
        """列出失败页面"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 构建查询条件
            conditions = []
            params = []
            
            if unresolved_only:
                conditions.append("is_resolved = FALSE")
            
            if category:
                conditions.append("category = %s")
                params.append(category)
            
            where_clause = ""
            if conditions:
                where_clause = "WHERE " + " AND ".join(conditions)
            
            query = f"""
                SELECT 
                    id, url, category, failure_reason, status_code, 
                    retry_count, last_attempt_time, is_resolved
                FROM failed_list_pages 
                {where_clause}
                ORDER BY last_attempt_time DESC
                LIMIT %s
            """
            
            params.append(limit)
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            if results:
                print(f"\n📋 失败页面列表 (显示前 {len(results)} 条):")
                print(f"{'ID':<5} {'URL':<50} {'分类':<12} {'失败原因':<20} {'状态码':<8} {'重试':<6} {'最后尝试时间':<16} {'已解决':<6}")
                print("-" * 125)
                
                for row in results:
                    # 格式化数据
                    page_id = row[0]
                    url = row[1][:47] + "..." if len(row[1]) > 50 else row[1]
                    category = row[2] or ""
                    reason = (row[3][:17] + "...") if row[3] and len(row[3]) > 20 else (row[3] or "")
                    status_code = row[4] or ""
                    retry_count = row[5]
                    last_attempt = row[6].strftime('%Y-%m-%d %H:%M') if row[6] else ""
                    is_resolved = '✅' if row[7] else '❌'
                    
                    print(f"{page_id:<5} {url:<50} {category:<12} {reason:<20} {status_code:<8} {retry_count:<6} {last_attempt:<16} {is_resolved:<6}")
            else:
                print("\n📋 没有找到符合条件的失败页面")
    
    def reset_failed_pages(self, category=None, max_retry_count=None):
        """重置失败页面状态"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            conditions = ["is_resolved = FALSE"]
            params = []
            
            if category:
                conditions.append("category = %s")
                params.append(category)
            
            if max_retry_count is not None:
                conditions.append("retry_count >= %s")
                params.append(max_retry_count)
            
            where_clause = "WHERE " + " AND ".join(conditions)
            
            update_sql = f"""
                UPDATE failed_list_pages 
                SET retry_count = 0, last_attempt_time = NOW(), updated_at = NOW()
                {where_clause}
            """
            
            cursor.execute(update_sql, params)
            affected_rows = cursor.rowcount
            conn.commit()
            
            print(f"✅ 已重置 {affected_rows} 个失败页面的重试状态")
    
    def mark_as_resolved(self, page_id=None, url=None):
        """标记页面为已解决"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            if page_id:
                cursor.execute("""
                    UPDATE failed_list_pages 
                    SET is_resolved = TRUE, resolved_at = NOW(), updated_at = NOW()
                    WHERE id = %s
                """, (page_id,))
            elif url:
                cursor.execute("""
                    UPDATE failed_list_pages 
                    SET is_resolved = TRUE, resolved_at = NOW(), updated_at = NOW()
                    WHERE url = %s
                """, (url,))
            else:
                print("❌ 请提供页面ID或URL")
                return
            
            affected_rows = cursor.rowcount
            conn.commit()
            
            if affected_rows > 0:
                print(f"✅ 已标记 {affected_rows} 个页面为已解决")
            else:
                print("❌ 没有找到匹配的页面")
    
    def delete_resolved_pages(self):
        """删除已解决的页面"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM failed_list_pages WHERE is_resolved = TRUE")
            affected_rows = cursor.rowcount
            conn.commit()
            
            print(f"🗑️ 已删除 {affected_rows} 个已解决的失败页面记录")
    
    def export_failed_pages(self, filename=None, unresolved_only=True):
        """导出失败页面到文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"failed_pages_{timestamp}.txt"
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            where_clause = "WHERE is_resolved = FALSE" if unresolved_only else ""
            
            cursor.execute(f"""
                SELECT url, category, failure_reason, retry_count, last_attempt_time
                FROM failed_list_pages 
                {where_clause}
                ORDER BY category, last_attempt_time DESC
            """)
            
            results = cursor.fetchall()
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"# 失败页面导出\n")
                f.write(f"# 导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 总计: {len(results)} 个页面\n\n")
                
                for result in results:
                    url, category, reason, retry_count, last_attempt = result
                    f.write(f"URL: {url}\n")
                    f.write(f"分类: {category}\n")
                    f.write(f"失败原因: {reason}\n")
                    f.write(f"重试次数: {retry_count}\n")
                    f.write(f"最后尝试: {last_attempt}\n")
                    f.write("-" * 50 + "\n")
            
            print(f"📄 已导出 {len(results)} 个失败页面到文件: {filename}")

def main():
    parser = argparse.ArgumentParser(description='失败列表页管理工具')
    parser.add_argument('action', choices=['stats', 'list', 'reset', 'resolve', 'delete', 'export'], 
                       help='操作类型')
    parser.add_argument('--category', help='指定分类')
    parser.add_argument('--limit', type=int, default=20, help='列表显示数量限制')
    parser.add_argument('--unresolved-only', action='store_true', help='只显示未解决的页面')
    parser.add_argument('--max-retry', type=int, help='最大重试次数过滤')
    parser.add_argument('--page-id', type=int, help='页面ID')
    parser.add_argument('--url', help='页面URL')
    parser.add_argument('--filename', help='导出文件名')
    
    args = parser.parse_args()
    
    manager = FailedPagesManager()
    
    try:
        if args.action == 'stats':
            manager.show_stats()
        
        elif args.action == 'list':
            manager.list_failed_pages(
                limit=args.limit, 
                unresolved_only=args.unresolved_only,
                category=args.category
            )
        
        elif args.action == 'reset':
            manager.reset_failed_pages(
                category=args.category,
                max_retry_count=args.max_retry
            )
        
        elif args.action == 'resolve':
            manager.mark_as_resolved(
                page_id=args.page_id,
                url=args.url
            )
        
        elif args.action == 'delete':
            manager.delete_resolved_pages()
        
        elif args.action == 'export':
            manager.export_failed_pages(
                filename=args.filename,
                unresolved_only=args.unresolved_only
            )
    
    except Exception as e:
        print(f"❌ 操作失败: {e}")

if __name__ == '__main__':
    main()