#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电影链接数据库管理脚本
用于查看、管理和统计电影链接数据
"""

import pymysql
import argparse
from datetime import datetime


class MovieLinkManager:
    def __init__(self, mysql_config=None):
        self.mysql_config = mysql_config or {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': 'root',
            'database': 'dytt2026',
            'charset': 'utf8mb4'
        }
    
    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.mysql_config)
    
    def show_statistics(self):
        """显示统计信息"""
        print("=== 电影链接数据库统计 ===")
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 总数统计
            cursor.execute('SELECT COUNT(*) FROM movie_links')
            total_count = cursor.fetchone()[0]
            print(f"总链接数: {total_count}")
            
            # 状态统计
            cursor.execute("""
                SELECT status, COUNT(*) as count
                FROM movie_links 
                GROUP BY status
                ORDER BY count DESC
            """)
            
            print("\n状态分布:")
            for status, count in cursor.fetchall():
                print(f"  {status}: {count}")
            
            # 分类统计
            cursor.execute("""
                SELECT category, COUNT(*) as count
                FROM movie_links 
                GROUP BY category
                ORDER BY count DESC
            """)
            
            print("\n分类分布:")
            for category, count in cursor.fetchall():
                print(f"  {category}: {count}")
            
            # 重试次数统计
            cursor.execute("""
                SELECT retry_count, COUNT(*) as count
                FROM movie_links 
                GROUP BY retry_count
                ORDER BY retry_count
            """)
            
            print("\n重试次数分布:")
            for retry_count, count in cursor.fetchall():
                print(f"  {retry_count} 次: {count}")
    
    def list_links(self, status=None, category=None, limit=20):
        """列出链接"""
        print(f"=== 电影链接列表 (限制 {limit} 条) ===")
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            query = 'SELECT id, title, url, category, status, retry_count, created_at FROM movie_links'
            params = []
            conditions = []
            
            if status:
                conditions.append('status = %s')
                params.append(status)
            
            if category:
                conditions.append('category = %s')
                params.append(category)
            
            if conditions:
                query += ' WHERE ' + ' AND '.join(conditions)
            
            query += ' ORDER BY created_at DESC LIMIT %s'
            params.append(limit)
            
            cursor.execute(query, params)
            
            print(f"{'ID':<5} {'标题':<30} {'分类':<10} {'状态':<10} {'重试':<5} {'创建时间':<20}")
            print("-" * 90)
            
            for row in cursor.fetchall():
                link_id, title, url, category, status, retry_count, created_at = row
                title = title[:28] + '...' if len(title) > 30 else title
                print(f"{link_id:<5} {title:<30} {category:<10} {status:<10} {retry_count:<5} {created_at:<20}")
    
    def reset_failed_links(self):
        """重置失败的链接状态"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE movie_links 
                SET status = 'pending', retry_count = 0, updated_at = CURRENT_TIMESTAMP
                WHERE status = 'failed'
            """)
            
            affected_rows = cursor.rowcount
            conn.commit()
            
            print(f"已重置 {affected_rows} 个失败链接的状态")
    
    def reset_crawling_links(self):
        """重置爬取中的链接状态"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE movie_links 
                SET status = 'pending', updated_at = CURRENT_TIMESTAMP
                WHERE status = 'crawling'
            """)
            
            affected_rows = cursor.rowcount
            conn.commit()
            
            print(f"已重置 {affected_rows} 个爬取中链接的状态")
    
    def delete_completed_links(self):
        """删除已完成的链接"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM movie_links WHERE status = "completed"')
            count = cursor.fetchone()[0]
            
            if count == 0:
                print("没有已完成的链接需要删除")
                return
            
            confirm = input(f"确定要删除 {count} 个已完成的链接吗? (y/N): ")
            if confirm.lower() == 'y':
                cursor.execute('DELETE FROM movie_links WHERE status = "completed"')
                conn.commit()
                print(f"已删除 {count} 个已完成的链接")
            else:
                print("操作已取消")
    
    def clear_all_links(self):
        """清空所有链接"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM movie_links')
            count = cursor.fetchone()[0]
            
            if count == 0:
                print("数据库中没有链接")
                return
            
            confirm = input(f"确定要删除所有 {count} 个链接吗? 此操作不可恢复! (y/N): ")
            if confirm.lower() == 'y':
                cursor.execute('DELETE FROM movie_links')
                conn.commit()
                print(f"已删除所有 {count} 个链接")
            else:
                print("操作已取消")
    
    def export_links(self, filename=None, status=None):
        """导出链接到文件"""
        if not filename:
            filename = f"movie_links_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            query = 'SELECT title, url, category, status FROM movie_links'
            params = []
            
            if status:
                query += ' WHERE status = %s'
                params.append(status)
            
            query += ' ORDER BY created_at DESC'
            
            cursor.execute(query, params)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("标题\tURL\t分类\t状态\n")
                for row in cursor.fetchall():
                    f.write("\t".join(row) + "\n")
            
            print(f"链接已导出到: {filename}")


def main():
    parser = argparse.ArgumentParser(description='电影链接数据库管理工具')
    parser.add_argument('--host', default='127.0.0.1', help='MySQL主机地址')
    parser.add_argument('--port', type=int, default=3306, help='MySQL端口')
    parser.add_argument('--user', default='root', help='MySQL用户名')
    parser.add_argument('--password', default='root', help='MySQL密码')
    parser.add_argument('--database', default='dytt2026', help='MySQL数据库名')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 统计命令
    subparsers.add_parser('stats', help='显示统计信息')
    
    # 列表命令
    list_parser = subparsers.add_parser('list', help='列出链接')
    list_parser.add_argument('--status', help='按状态过滤')
    list_parser.add_argument('--category', help='按分类过滤')
    list_parser.add_argument('--limit', type=int, default=20, help='限制显示数量')
    
    # 重置命令
    subparsers.add_parser('reset-failed', help='重置失败的链接')
    subparsers.add_parser('reset-crawling', help='重置爬取中的链接')
    
    # 删除命令
    subparsers.add_parser('delete-completed', help='删除已完成的链接')
    subparsers.add_parser('clear-all', help='清空所有链接')
    
    # 导出命令
    export_parser = subparsers.add_parser('export', help='导出链接')
    export_parser.add_argument('--filename', help='输出文件名')
    export_parser.add_argument('--status', help='按状态过滤')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    mysql_config = {
        'host': args.host,
        'port': args.port,
        'user': args.user,
        'password': args.password,
        'database': args.database,
        'charset': 'utf8mb4'
    }
    
    manager = MovieLinkManager(mysql_config)
    
    if args.command == 'stats':
        manager.show_statistics()
    elif args.command == 'list':
        manager.list_links(args.status, args.category, args.limit)
    elif args.command == 'reset-failed':
        manager.reset_failed_links()
    elif args.command == 'reset-crawling':
        manager.reset_crawling_links()
    elif args.command == 'delete-completed':
        manager.delete_completed_links()
    elif args.command == 'clear-all':
        manager.clear_all_links()
    elif args.command == 'export':
        manager.export_links(args.filename, args.status)


if __name__ == '__main__':
    main()