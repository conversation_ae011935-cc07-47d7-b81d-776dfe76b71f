#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电影详情爬取器运行脚本
第二阶段：从数据库中读取链接并爬取电影详情
"""

import os
import sys
import time
import logging
import logging.config
import pymysql
from datetime import datetime
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dyttspider'))

from dyttspider.spiders.detail_crawler_spider import DetailCrawlerSpider


def check_pending_links(logger=None):
    """检查是否有待爬取的链接"""
    try:
        mysql_config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': 'root',
            'database': 'dytt2026',
            'charset': 'utf8mb4'
        }
        
        connection = pymysql.connect(**mysql_config)
        cursor = connection.cursor()
        
        cursor.execute("""
            SELECT COUNT(*) FROM movie_links 
            WHERE status = 'pending' AND retry_count < 3
        """)
        
        count = cursor.fetchone()[0]
        connection.close()
        
        if logger:
            logger.info(f"Found {count} pending links to crawl")
        
        return count
    except Exception as e:
        error_msg = f"Failed to check pending links: {e}"
        if logger:
            logger.error(error_msg, exc_info=True)
        else:
            print(error_msg)
        return 0


def get_link_statistics(logger=None):
    """获取链接统计信息"""
    try:
        mysql_config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': 'root',
            'database': 'dytt2026',
            'charset': 'utf8mb4'
        }
        
        connection = pymysql.connect(**mysql_config)
        cursor = connection.cursor()
        
        cursor.execute("""
            SELECT status, COUNT(*) as count
            FROM movie_links 
            GROUP BY status
        """)
        
        stats = dict(cursor.fetchall())
        connection.close()
        
        if logger:
            logger.info(f"Link statistics: {stats}")
        
        return stats
    except Exception as e:
        error_msg = f"Failed to get statistics: {e}"
        if logger:
            logger.error(error_msg, exc_info=True)
        else:
            print(error_msg)
        return {}


def run_detail_crawler(batch_size=10, logger=None):
    """运行详情爬取器"""
    if not logger:
        logger = logging.getLogger('detail_crawler')
    
    start_time = datetime.now()
    logger.info("=" * 60)
    logger.info("Starting Detail Crawler Spider")
    logger.info(f"Start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"Batch size: {batch_size}")
    logger.info("=" * 60)
    
    try:
        pending_count = check_pending_links(logger)
        
        if pending_count == 0:
            logger.info("No pending movie links to crawl")
            return False
        
        actual_batch = min(batch_size, pending_count)
        logger.info(f"Found {pending_count} pending movie links")
        logger.info(f"Will crawl {actual_batch} links in this batch")
        
        # 获取项目设置
        settings = get_project_settings()
        
        # 自定义设置
        settings.update({
            'ROBOTSTXT_OBEY': False,
            'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'DOWNLOAD_DELAY': 3,
            'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
            'LOG_LEVEL': 'INFO',
            'ITEM_PIPELINES': {
                'dyttspider.pipelines.MoviePipeline': 300,
            }
        })
        
        logger.info("Spider configuration:")
        logger.info(f"  - Download delay: {settings.get('DOWNLOAD_DELAY')} seconds")
        logger.info(f"  - Concurrent requests per domain: {settings.get('CONCURRENT_REQUESTS_PER_DOMAIN')}")
        logger.info(f"  - User agent: {settings.get('USER_AGENT')[:50]}...")
        
        # 创建爬虫进程
        process = CrawlerProcess(settings)
        
        # 添加爬虫
        process.crawl(DetailCrawlerSpider, batch_size=batch_size)
        
        logger.info("Starting crawl process...")
        
        # 开始爬取
        process.start()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("=" * 60)
        logger.info("Detail Crawler Spider Completed")
        logger.info(f"End time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Total duration: {duration}")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"Error running detail crawler: {e}", exc_info=True)
        raise


def run_scheduler(batch_size=10, interval=300):
    """运行调度器，定期爬取电影详情"""
    # Setup logging using logging.conf
    project_root = os.path.dirname(__file__)
    logging_conf_path = os.path.join(project_root, 'logging.conf')
    
    # Ensure logs directory exists
    logs_dir = os.path.join(project_root, 'logs')
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
    
    # Change to project root directory for relative paths in logging.conf
    original_cwd = os.getcwd()
    os.chdir(project_root)
    
    try:
        if os.path.exists(logging_conf_path):
            logging.config.fileConfig(logging_conf_path, disable_existing_loggers=False)
    finally:
        # Restore original working directory
        os.chdir(original_cwd)
    
    logger = logging.getLogger('detail_crawler')
    
    logger.info("=" * 60)
    logger.info("Starting Detail Crawler Scheduler")
    logger.info(f"Batch size: {batch_size}")
    logger.info(f"Interval: {interval} seconds")
    logger.info("Press Ctrl+C to stop scheduler")
    logger.info("=" * 60)
    
    round_count = 0
    
    try:
        while True:
            round_count += 1
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            logger.info(f"\n{'='*40} Round {round_count} {'='*40}")
            logger.info(f"Starting new crawl round at {current_time}")
            
            # 显示统计信息
            stats = get_link_statistics(logger)
            
            # 运行爬虫
            success = run_detail_crawler(batch_size, logger)
            
            if success:
                logger.info(f"Round {round_count} completed successfully")
                logger.info(f"Waiting {interval} seconds before next round...")
                time.sleep(interval)
            else:
                logger.info(f"No more pending links found in round {round_count}")
                logger.info(f"Waiting {interval} seconds before rechecking...")
                time.sleep(interval)
                
    except KeyboardInterrupt:
        logger.info(f"\nScheduler stopped by user after {round_count} rounds")
        logger.info("Scheduler shutdown completed")
    except Exception as e:
        logger.error(f"Scheduler error in round {round_count}: {e}", exc_info=True)
        raise


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='电影详情爬取器')
    parser.add_argument('--mode', choices=['once', 'scheduler'], default='once',
                       help='运行模式: once(单次运行) 或 scheduler(调度器模式)')
    parser.add_argument('--batch-size', type=int, default=10,
                       help='每批处理的链接数量 (默认: 10)')
    parser.add_argument('--interval', type=int, default=300,
                       help='调度器间隔时间(秒) (默认: 300)')
    
    args = parser.parse_args()
    
    if args.mode == 'once':
        # Setup logging for single run mode
        project_root = os.path.dirname(__file__)
        logging_conf_path = os.path.join(project_root, 'logging.conf')
        
        # Ensure logs directory exists
        logs_dir = os.path.join(project_root, 'logs')
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        # Change to project root directory for relative paths in logging.conf
        original_cwd = os.getcwd()
        os.chdir(project_root)
        
        try:
            if os.path.exists(logging_conf_path):
                logging.config.fileConfig(logging_conf_path, disable_existing_loggers=False)
        finally:
            # Restore original working directory
            os.chdir(original_cwd)
        
        logger = logging.getLogger('detail_crawler')
        logger.info("Starting Detail Crawler in single run mode")
        run_detail_crawler(args.batch_size)
    else:
        # Scheduler mode handles its own logging setup
        run_scheduler(args.batch_size, args.interval)