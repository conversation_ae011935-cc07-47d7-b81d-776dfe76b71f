#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电影详情爬虫运行脚本
第二阶段：爬取电影详情信息并存储到数据库
"""

import os
import sys
import logging
import logging.config
from datetime import datetime
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dyttspider'))

from dyttspider.spiders.dytt_spider import D<PERSON><PERSON><PERSON><PERSON><PERSON>


def run_dytt_spider():
    """运行电影详情爬虫"""
    # Setup logging using logging.conf
    project_root = os.path.dirname(__file__)
    logging_conf_path = os.path.join(project_root, 'logging.conf')
    
    # Ensure logs directory exists
    logs_dir = os.path.join(project_root, 'logs')
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
    
    # Change to project root directory for relative paths in logging.conf
    original_cwd = os.getcwd()
    os.chdir(project_root)
    
    try:
        if os.path.exists(logging_conf_path):
            logging.config.fileConfig(logging_conf_path, disable_existing_loggers=False)
    finally:
        # Restore original working directory
        os.chdir(original_cwd)
    
    logger = logging.getLogger('dytt_spider')
    
    start_time = datetime.now()
    logger.info("=" * 60)
    logger.info("Starting DYTT Movie Detail Spider")
    logger.info(f"Start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)
    
    try:
        # 获取项目设置
        settings = get_project_settings()
        
        # 自定义设置
        settings.update({
            'ROBOTSTXT_OBEY': False,
            'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'DOWNLOAD_DELAY': 2,
            'CONCURRENT_REQUESTS_PER_DOMAIN': 2,
            'LOG_LEVEL': 'INFO',
            'ITEM_PIPELINES': {
                'dyttspider.pipelines.MoviePipeline': 300,
            },
            # 反爬虫中间件
            'DOWNLOADER_MIDDLEWARES': {
                'dyttspider.anti_ban_middlewares.RandomUserAgentMiddleware': 400,
                'dyttspider.anti_ban_middlewares.ProxyMiddleware': 410,
                'dyttspider.anti_ban_middlewares.RetryMiddleware': 420,
            },
            # 启用AutoThrottle自动调节下载延迟
            'AUTOTHROTTLE_ENABLED': True,
            'AUTOTHROTTLE_START_DELAY': 1,
            'AUTOTHROTTLE_MAX_DELAY': 10,
            'AUTOTHROTTLE_TARGET_CONCURRENCY': 2.0,
            'AUTOTHROTTLE_DEBUG': True,
        })
        
        logger.info("Spider configuration:")
        logger.info(f"  - Download delay: {settings.get('DOWNLOAD_DELAY')} seconds")
        logger.info(f"  - Concurrent requests per domain: {settings.get('CONCURRENT_REQUESTS_PER_DOMAIN')}")
        logger.info(f"  - User agent: {settings.get('USER_AGENT')[:50]}...")
        logger.info(f"  - AutoThrottle enabled: {settings.get('AUTOTHROTTLE_ENABLED')}")
        logger.info(f"  - Item count limit: {settings.get('CLOSESPIDER_ITEMCOUNT', 'No limit')}")
        
        # 创建爬虫进程
        process = CrawlerProcess(settings)
        
        # 添加爬虫
        process.crawl(DyttSpider)
        
        logger.info("Starting crawl process...")
        
        # 开始爬取
        process.start()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("=" * 60)
        logger.info("DYTT Movie Detail Spider Completed")
        logger.info(f"End time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Total duration: {duration}")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"Error running DYTT spider: {e}", exc_info=True)
        raise


if __name__ == '__main__':
    run_dytt_spider()