#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新数据库表字段备注脚本
为现有的数据库表添加字段备注，提高数据库可读性和维护性
"""

import pymysql
import sys

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'dytt2026',
    'charset': 'utf8mb4'
}

def update_movie_links_comments():
    """更新movie_links表的字段备注"""
    alter_statements = [
        "ALTER TABLE movie_links MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID，用于唯一标识每个电影链接'",
        "ALTER TABLE movie_links MODIFY COLUMN title VARCHAR(500) COMMENT '电影标题'",
        "ALTER TABLE movie_links MODIFY COLUMN url VARCHAR(500) UNIQUE COMMENT '电影详情页链接，唯一约束'",
        "ALTER TABLE movie_links MODIFY COLUMN category VARCHAR(200) COMMENT '电影分类（最新电影/华语电影等）'",
        "ALTER TABLE movie_links MODIFY COLUMN list_page_url VARCHAR(500) COMMENT '来源列表页URL'",
        "ALTER TABLE movie_links MODIFY COLUMN crawl_time VARCHAR(100) COMMENT '爬取时间'",
        "ALTER TABLE movie_links MODIFY COLUMN status VARCHAR(20) DEFAULT 'pending' COMMENT '爬取状态：pending-待爬取，crawling-爬取中，completed-已完成，failed-失败'",
        "ALTER TABLE movie_links MODIFY COLUMN retry_count INT DEFAULT 0 COMMENT '重试次数，用于控制失败重试'",
        "ALTER TABLE movie_links MODIFY COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间'",
        "ALTER TABLE movie_links MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'",
        "ALTER TABLE movie_links COMMENT='电影链接表，存储待爬取的电影详情页链接'"
    ]
    return alter_statements

def update_movies_comments():
    """更新movies表的字段备注（简化版）"""
    alter_statements = [
        "ALTER TABLE movies MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID，电影唯一标识'",
        "ALTER TABLE movies MODIFY COLUMN title VARCHAR(500) NOT NULL COMMENT '电影标题'",
        "ALTER TABLE movies MODIFY COLUMN url VARCHAR(500) COMMENT '电影详情页链接'",
        "ALTER TABLE movies MODIFY COLUMN publish_time VARCHAR(100) COMMENT '发布时间'",
        "ALTER TABLE movies MODIFY COLUMN category VARCHAR(200) COMMENT '电影分类'",
        "ALTER TABLE movies MODIFY COLUMN description TEXT COMMENT '电影描述/简介'",
        "ALTER TABLE movies MODIFY COLUMN download_links JSON COMMENT '下载链接JSON数据'",
        "ALTER TABLE movies MODIFY COLUMN rating VARCHAR(50) COMMENT '电影评分'",
        "ALTER TABLE movies MODIFY COLUMN year VARCHAR(10) COMMENT '电影年份'",
        "ALTER TABLE movies MODIFY COLUMN director VARCHAR(500) COMMENT '导演信息'",
        "ALTER TABLE movies MODIFY COLUMN actors TEXT COMMENT '演员信息'",
        "ALTER TABLE movies MODIFY COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间'",
        "ALTER TABLE movies MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'",
        "ALTER TABLE movies COMMENT='电影信息表（简化版）'"
    ]
    return alter_statements

def update_failed_list_pages_comments():
    """更新failed_list_pages表的字段备注（如果存在）"""
    alter_statements = [
        "ALTER TABLE failed_list_pages MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID'",
        "ALTER TABLE failed_list_pages MODIFY COLUMN url VARCHAR(500) NOT NULL COMMENT '失败的列表页URL'",
        "ALTER TABLE failed_list_pages MODIFY COLUMN category VARCHAR(200) COMMENT '分类（最新电影/华语电影等）'",
        "ALTER TABLE failed_list_pages MODIFY COLUMN failure_reason VARCHAR(500) COMMENT '失败原因'",
        "ALTER TABLE failed_list_pages MODIFY COLUMN status_code VARCHAR(20) COMMENT 'HTTP状态码'",
        "ALTER TABLE failed_list_pages MODIFY COLUMN retry_count INT DEFAULT 0 COMMENT '重试次数'",
        "ALTER TABLE failed_list_pages MODIFY COLUMN last_attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后尝试时间'",
        "ALTER TABLE failed_list_pages MODIFY COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'",
        "ALTER TABLE failed_list_pages MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'",
        "ALTER TABLE failed_list_pages MODIFY COLUMN is_resolved BOOLEAN DEFAULT FALSE COMMENT '是否已解决'",
        "ALTER TABLE failed_list_pages MODIFY COLUMN resolved_at TIMESTAMP NULL COMMENT '解决时间'",
        "ALTER TABLE failed_list_pages MODIFY COLUMN notes TEXT COMMENT '备注信息'",
        "ALTER TABLE failed_list_pages COMMENT='失败列表页记录表'"
    ]
    return alter_statements

def check_table_exists(cursor, table_name):
    """检查表是否存在"""
    cursor.execute("""
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = %s AND table_name = %s
    """, (DB_CONFIG['database'], table_name))
    return cursor.fetchone()[0] > 0

def update_table_comments():
    """更新所有表的字段备注"""
    try:
        # 连接数据库
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("🔄 开始更新数据库表字段备注...")
        print("=" * 60)
        
        # 定义要更新的表和对应的更新函数
        tables_to_update = [
            ('movie_links', update_movie_links_comments, '电影链接表'),
            ('movies', update_movies_comments, '电影信息表'),
            ('failed_list_pages', update_failed_list_pages_comments, '失败列表页表')
        ]
        
        for table_name, update_func, description in tables_to_update:
            if check_table_exists(cursor, table_name):
                print(f"\n📝 更新 {description} ({table_name}) 的字段备注...")
                
                alter_statements = update_func()
                success_count = 0
                
                for statement in alter_statements:
                    try:
                        cursor.execute(statement)
                        success_count += 1
                    except Exception as e:
                        print(f"   ⚠️  执行失败: {statement}")
                        print(f"      错误: {e}")
                
                print(f"   ✅ 成功执行 {success_count}/{len(alter_statements)} 条语句")
                
            else:
                print(f"\n⚠️  表 {table_name} 不存在，跳过更新")
        
        # 提交更改
        connection.commit()
        cursor.close()
        connection.close()
        
        print(f"\n🎉 数据库表字段备注更新完成！")
        print("\n💡 提示: 可以使用 SHOW CREATE TABLE 命令查看更新后的表结构")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        sys.exit(1)

def show_table_structure(table_name):
    """显示表结构"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        if check_table_exists(cursor, table_name):
            print(f"\n📋 {table_name} 表结构:")
            print("-" * 80)
            
            cursor.execute(f"SHOW CREATE TABLE {table_name}")
            result = cursor.fetchone()
            if result:
                print(result[1])
        else:
            print(f"❌ 表 {table_name} 不存在")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 查看表结构失败: {e}")

def list_all_tables():
    """列出数据库中的所有表"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()

        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()

        print(f"\n📋 数据库 {DB_CONFIG['database']} 中的所有表:")
        print("-" * 50)
        for table in tables:
            table_name = table[0]

            # 获取表备注
            cursor.execute(f"""
                SELECT table_comment
                FROM information_schema.tables
                WHERE table_schema = %s AND table_name = %s
            """, (DB_CONFIG['database'], table_name))

            comment_result = cursor.fetchone()
            comment = comment_result[0] if comment_result and comment_result[0] else "无备注"

            print(f"  {table_name:<20} - {comment}")

        cursor.close()
        connection.close()

    except Exception as e:
        print(f"❌ 获取表列表失败: {e}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='更新数据库表字段备注')
    parser.add_argument('--show', type=str, help='显示指定表的结构')
    parser.add_argument('--update', action='store_true', help='更新所有表的字段备注')
    parser.add_argument('--list', action='store_true', help='列出所有表')

    args = parser.parse_args()

    if args.show:
        show_table_structure(args.show)
    elif args.update:
        update_table_comments()
    elif args.list:
        list_all_tables()
    else:
        print("使用方法:")
        print("  python update_table_comments.py --update          # 更新所有表的字段备注")
        print("  python update_table_comments.py --show movie_links # 显示指定表的结构")
        print("  python update_table_comments.py --list            # 列出所有表")
